package com.touptek.xcamview.activity.browse.imagemanagement

import android.graphics.Bitmap
import android.os.Bundle
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.DialogFragment
import com.touptek.xcamview.R
import com.touptek.xcamview.databinding.ImageViewerBinding
import com.touptek.video.internal.TpImageLoader
import java.util.ArrayList
import com.touptek.measurerealize.MeasurementManager
import android.util.Log

class TpImageDecodeDialogFragment : DialogFragment() {
    private lateinit var binding: ImageViewerBinding
    private lateinit var imagePaths: List<String>
    private var currentPosition = 0

    // 🎯 测量工具栏相关变量 - 改为直接View控制
    // private var measurementDialogFragment: TpMeasurementDialogFragment? = null // 不再使用DialogFragment

    // 角度测量相关变量 - 使用封装的MeasurementManager
    private lateinit var measurementManager: MeasurementManager
    private var isMeasuringAngle = false
    private var isMeasuringFourPointAngle = false
    private var isMeasuringPoint = false
    private var isMeasuringLine = false
    private var currentBitmap: Bitmap? = null
    
    // 界面状态枚举 - 分别控制顶部和底部
    private enum class TopToolbarState {
        VISIBLE,    // 顶部工具栏显示
        HIDDEN      // 顶部工具栏隐藏
    }

    private enum class BottomButtonState {
        VISIBLE,    // 底部按钮显示
        HIDDEN      // 底部按钮隐藏
    }

    private var topToolbarState = TopToolbarState.VISIBLE
    private var bottomButtonState = BottomButtonState.VISIBLE

    companion object {
        private const val ARG_IMAGE_PATHS = "image_paths"
        private const val ARG_CURRENT_POSITION = "current_position"

        fun newInstance(imagePaths: List<String>, currentPosition: Int): TpImageDecodeDialogFragment {
            val fragment = TpImageDecodeDialogFragment()
            val args = Bundle().apply {
                putStringArrayList(ARG_IMAGE_PATHS, ArrayList(imagePaths))
                putInt(ARG_CURRENT_POSITION, currentPosition)
            }
            fragment.arguments = args
            return fragment
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = ImageViewerBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        imagePaths = arguments?.getStringArrayList(ARG_IMAGE_PATHS) ?: run {
            dismiss()
            return
        }
        currentPosition = arguments?.getInt(ARG_CURRENT_POSITION, 0) ?: 0

        initializeMeasurement()
        loadCurrentImage()
        setupClickListeners()
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NORMAL, R.style.FullScreenDialog)
    }

    override fun onStart() {
        super.onStart()
        dialog?.window?.let { window ->
            window.setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT)
        }
    }

    private fun setupClickListeners() {
        // 完全移除 OnClickListener，避免与 TpImageView 内部手势冲突
        // binding.imageView.setOnClickListener { toggleButtons() }
        
        // 只使用 TpImageView 的单击事件处理
        setupImageViewClickHandler()

        // 🎯 初始化时显示顶部工具栏（用户可通过触摸控制）
        showMeasurementToolbar()

        binding.btnPrevious.setOnClickListener { showPreviousImage() }
        binding.btnNext.setOnClickListener { showNextImage() }
        binding.btnBack.setOnClickListener { dismiss() }

        // 🎯 设置测量工具栏按钮点击事件
        setupMeasurementToolbarButtons()
    }

    private fun setupImageViewClickHandler() {
        // 初始化时启用单击监听器
        enableSingleTapListener()
    }

    /**
     * 🎯 设置测量工具栏按钮点击事件 - 完整的23个按钮
     */
    private fun setupMeasurementToolbarButtons() {
        // 第一行：基础功能按钮
        binding.btnCalibration.setOnClickListener {
            Log.d("ImageDialog", "🎯 Calibration button clicked")
            // TODO: 添加校准功能
        }

        binding.btnDeleteMeasurement.setOnClickListener {
            Log.d("ImageDialog", "🗑️ Delete measurement button clicked - current state: isMeasuringAngle=$isMeasuringAngle")
            //删除功能
            deleteSelectedMeasurement()
        }

        // 角度测量相关 - 支持混合模式
        binding.btnAngle.setOnClickListener {
            Log.d("ImageDialog", "🎯 Angle measurement button clicked - current state: isMeasuringAngle=$isMeasuringAngle, isMeasuringFourPointAngle=$isMeasuringFourPointAngle")
            startAngleMeasurementMixed()
        }

        binding.btnAngleFour.setOnClickListener {
            Log.d("ImageDialog", "🎯 Four-point angle measurement button clicked - current state: isMeasuringAngle=$isMeasuringAngle, isMeasuringFourPointAngle=$isMeasuringFourPointAngle")
            startFourPointAngleMeasurementMixed()
        }

        // 点和线测量
        binding.btnPoint.setOnClickListener {
            Log.d("ImageDialog", "🎯 Point button clicked - current state: isMeasuringAngle=$isMeasuringAngle, isMeasuringFourPointAngle=$isMeasuringFourPointAngle, isMeasuringPoint=$isMeasuringPoint")
            startPointMeasurement()
        }

        binding.btnArbLine.setOnClickListener {
            Log.d("ImageDialog", "📏 Line measurement button clicked - current state: isMeasuringAngle=$isMeasuringAngle, isMeasuringFourPointAngle=$isMeasuringFourPointAngle, isMeasuringPoint=$isMeasuringPoint, isMeasuringLine=$isMeasuringLine")
            startLineMeasurement()
        }

        binding.btnThreeLine.setOnClickListener {
            Log.d("ImageDialog", "🎯 Three line button clicked")
            // TODO: 添加三线测量功能
        }

        binding.btnHorizonLine.setOnClickListener {
            Log.d("ImageDialog", "🎯 Horizon line button clicked")
            // TODO: 添加水平线测量功能
        }

        binding.btnVerticalLine.setOnClickListener {
            Log.d("ImageDialog", "🎯 Vertical line button clicked")
            // TODO: 添加垂直线测量功能
        }

        binding.btnParallelLine.setOnClickListener {
            Log.d("ImageDialog", "🎯 Parallel line button clicked")
            // TODO: 添加平行线测量功能
        }

        binding.btnThreeVertical.setOnClickListener {
            Log.d("ImageDialog", "🎯 Three vertical button clicked")
            // TODO: 添加三垂直测量功能
        }

        // 矩形测量
        binding.btnRectangle.setOnClickListener {
            Log.d("ImageDialog", "🎯 Rectangle button clicked")
            // TODO: 添加矩形测量功能
        }

        binding.btnThreeRectangle.setOnClickListener {
            Log.d("ImageDialog", "🎯 Three rectangle button clicked")
            // TODO: 添加三矩形测量功能
        }

        // 椭圆测量
        binding.btnEllipse.setOnClickListener {
            Log.d("ImageDialog", "🎯 Ellipse button clicked")
            // TODO: 添加椭圆测量功能
        }

        binding.btnFiveEllipse.setOnClickListener {
            Log.d("ImageDialog", "🎯 Five ellipse button clicked")
            // TODO: 添加五点椭圆测量功能
        }

        // 圆形测量
        binding.btnCenterCircle.setOnClickListener {
            Log.d("ImageDialog", "🎯 Center circle button clicked")
            // TODO: 添加中心圆测量功能
        }

        binding.btnThreeCircle.setOnClickListener {
            Log.d("ImageDialog", "🎯 Three circle button clicked")
            // TODO: 添加三点圆测量功能
        }

        binding.btnAnnulus.setOnClickListener {
            Log.d("ImageDialog", "🎯 Annulus button clicked")
            // TODO: 添加环形测量功能
        }

        binding.btnAnnulus2.setOnClickListener {
            Log.d("ImageDialog", "🎯 Annulus2 button clicked")
            // TODO: 添加环形2测量功能
        }

        binding.btnTwocircles.setOnClickListener {
            Log.d("ImageDialog", "🎯 Two circles button clicked")
            // TODO: 添加双圆测量功能
        }

        binding.btnThreeTwocircles.setOnClickListener {
            Log.d("ImageDialog", "🎯 Three two circles button clicked")
            // TODO: 添加三点双圆测量功能
        }

        binding.btnArc.setOnClickListener {
            Log.d("ImageDialog", "🎯 Arc button clicked")
            // TODO: 添加弧形测量功能
        }
    }

    private fun loadCurrentImage() {
        if (currentPosition in imagePaths.indices) {
            TpImageLoader.loadFullImage(imagePaths[currentPosition], binding.imageView)
            updateButtonStates()
            updateCurrentBitmap()
        }
    }

    private fun showPreviousImage() {
        if (currentPosition > 0) {
            currentPosition--
            loadCurrentImage()
        }
    }

    private fun showNextImage() {
        if (currentPosition < imagePaths.size - 1) {
            currentPosition++
            loadCurrentImage()
        }
    }

    private fun updateButtonStates() {
        binding.btnPrevious.isEnabled = currentPosition > 0
        binding.btnNext.isEnabled = currentPosition < imagePaths.size - 1
    }

    private fun cycleBottomButtonsState() {
        // 🎯 切换底部按钮状态
        when (bottomButtonState) {
            BottomButtonState.HIDDEN -> {
                // 隐藏状态 → 显示底部按钮
                showBottomButtons()
                bottomButtonState = BottomButtonState.VISIBLE
                Log.d("ImageDialog", "🎯 Bottom buttons: visible")
            }
            BottomButtonState.VISIBLE -> {
                // 显示状态 → 隐藏底部按钮
                hideBottomButtons()
                bottomButtonState = BottomButtonState.HIDDEN
                Log.d("ImageDialog", "🎯 Bottom buttons: hidden")
            }
        }

        Log.d("ImageDialog", "✅ Bottom buttons state cycled - current: $bottomButtonState")
    }

    private fun cycleTopToolbarState() {
        // 🎯 切换顶部工具栏状态
        when (topToolbarState) {
            TopToolbarState.HIDDEN -> {
                // 隐藏状态 → 显示顶部工具栏
                showMeasurementToolbar()
                topToolbarState = TopToolbarState.VISIBLE
                Log.d("ImageDialog", "🎯 Top toolbar: visible")
            }
            TopToolbarState.VISIBLE -> {
                // 显示状态 → 隐藏顶部工具栏
                hideMeasurementToolbar()
                topToolbarState = TopToolbarState.HIDDEN
                Log.d("ImageDialog", "🎯 Top toolbar: hidden")
            }
        }

        Log.d("ImageDialog", "✅ Top toolbar state cycled - current: $topToolbarState")
    }

    private fun showBottomButtons() {
        binding.buttonPanel.visibility = View.VISIBLE
    }

    private fun hideBottomButtons() {
        binding.buttonPanel.visibility = View.GONE
    }

    private fun showMeasurementToolbar() {
        // 🎯 直接控制View可见性，避免DialogFragment的InputEventReceiver问题
        binding.measurementToolbar.visibility = View.VISIBLE
        Log.d("ImageDialog", "✅ Measurement toolbar shown successfully (View visibility)")
    }

    private fun hideMeasurementToolbar() {
        // 🎯 直接控制View可见性，避免DialogFragment的InputEventReceiver问题
        binding.measurementToolbar.visibility = View.GONE
        Log.d("ImageDialog", "✅ Measurement toolbar hidden successfully (View visibility)")
    }

    /**
     * � 强制工具栏状态 - 确保测量工具栏显示并锁定
     */
    private fun forceToolbarState() {
        // 确保顶部工具栏显示（测量模式需要工具栏）
        showMeasurementToolbar()
        topToolbarState = TopToolbarState.VISIBLE
        // 底部按钮保持当前状态不变
        Log.d("ImageDialog", "🔒 Top toolbar forced visible - bottom buttons unchanged")
    }



    /**
     * ✅ 启用单击监听器 - 恢复正常UI交互
     */
    private fun enableSingleTapListener() {
        // 🎯 新逻辑：在单击回调中检测顶部和底部区域触摸
        binding.imageView.setOnSingleTapListener { event ->
            // 🛡️ 测量模式保护：如果正在拖拽测量点，不触发UI状态切换
            if (isDraggingMeasurementPoint()) {
                Log.d("ImageDialog", "🛡️ Dragging measurement point - UI state change blocked")
                return@setOnSingleTapListener
            }

            val screenHeight = binding.root.height
            val touchY = event.y
            val topThreshold = screenHeight * 0.2f    // 顶部20%区域
            val bottomThreshold = screenHeight * 0.8f // 底部20%区域

            when {
                touchY < topThreshold -> {
                    Log.d("ImageDialog", "🎯 Top area touched at (${event.x}, ${event.y}) - cycling top toolbar")
                    cycleTopToolbarState()
                }
                touchY > bottomThreshold -> {
                    Log.d("ImageDialog", "🎯 Bottom area touched at (${event.x}, ${event.y}) - cycling bottom buttons")
                    cycleBottomButtonsState()
                }
                else -> {
                    Log.d("ImageDialog", "🎯 Middle area tapped at (${event.x}, ${event.y}) - no UI state change")
                }
            }
        }

        Log.d("ImageDialog", "✅ Single tap listener enabled - top and bottom touch controlled with measurement protection")
    }



    /**
     * �🛡️ 保护测量模式 - 添加测量模式专用保护机制
     */
    private fun protectMeasurementMode() {
        // 确保测量状态标记正确设置（根据当前测量类型）
        // 注意：这个方法会被两种测量模式调用，所以不能硬编码状态

        // ✅ 保持单击监听器启用，允许UI状态切换
        // 移除完全UI锁定，改为智能条件判断

        Log.d("ImageDialog", "🛡️ Measurement mode protection activated - conditional UI control")
    }

    /**
     * � 检查是否正在拖拽测量点
     */
    private fun isDraggingMeasurementPoint(): Boolean {
        return if (::measurementManager.isInitialized) {
            // 通过MeasurementManager检查拖拽状态（支持三点和四点角度测量）
            measurementManager.isDraggingPoint()
        } else {
            false
        }
    }

    /**
     * ��🚀 初始化测量功能 - 使用封装的MeasurementManager
     */
    private fun initializeMeasurement() {
        // 创建MeasurementManager实例
        measurementManager = MeasurementManager(
            context = requireContext(),
            imageView = binding.imageView,
            overlayView = binding.measurementOverlay,
            statusTextView = null // 可以传入状态显示TextView
        )

        Log.d("ImageDialog", "🚀 MeasurementManager initialized")
    }

    /**
     * 🎯 开始角度测量 - 混合模式支持
     */
    fun startAngleMeasurementMixed() {
        Log.d("ImageDialog", "🎯 Angle measurement button clicked - current state: isMeasuringAngle=$isMeasuringAngle, isMeasuringFourPointAngle=$isMeasuringFourPointAngle")

        // 🎯 确保测量工具栏显示
        forceToolbarState()

        // 确保有位图数据
        if (currentBitmap == null) {
            updateCurrentBitmap()
        }

        // 如果还是没有位图，创建一个默认的
        val bitmap = currentBitmap ?: run {
            Log.w("ImageDialog", "⚠️ No bitmap available, creating default")
            Bitmap.createBitmap(1000, 1000, Bitmap.Config.ARGB_8888)
        }

        try {
            if (!isMeasuringAngle) {
                // 🔄 混合共存模式：如果四点角度测量存在，将其设为未激活状态
                if (isMeasuringFourPointAngle) {
                    Log.d("ImageDialog", "🔄 Deactivating four-point angle measurement, keeping it on display")
                    measurementManager.deactivateFourPointAngleMeasurement()
                }

                // 🚀 第一次点击：启动测量模式
                Log.d("ImageDialog", "🎯 First click - starting angle measurement mode...")

                // 🛡️ 设置测量模式保护（条件性）
                protectMeasurementMode()

                // 🚀 初始化MeasurementManager
                measurementManager.initialize(bitmap)

                // 🎯 开始角度测量模式
                if (measurementManager.startAngleMeasurement()) {
                    isMeasuringAngle = true // 🔄 同步Fragment状态
                    Log.d("ImageDialog", "✅ Angle measurement mode started - first angle created, isMeasuringAngle=$isMeasuringAngle")
                } else {
                    Log.e("ImageDialog", "❌ Failed to start angle measurement")
                    // 如果启动失败，重置测量状态
                    isMeasuringAngle = false
                }
            } else {
                // 🎯 后续点击：在测量模式下添加新角度
                Log.d("ImageDialog", "🎯 Subsequent click - adding new angle in measurement mode...")

                if (measurementManager.addNewAngleMeasurement()) {
                    Log.d("ImageDialog", "✅ New angle added successfully")
                } else {
                    Log.e("ImageDialog", "❌ Failed to add new angle")
                }
            }

        } catch (e: Exception) {
            Log.e("ImageDialog", "❌ Failed to handle angle measurement: ${e.message}")
            e.printStackTrace()
        }
    }

    /**
     * 🎯 开始四点角度测量 - 与三点角度测量保持一致的架构
     */
    fun startFourPointAngleMeasurement() {
        Log.d("ImageDialog", "🎯 Four-point angle measurement button clicked - current state: isMeasuringFourPointAngle=$isMeasuringFourPointAngle")

        // 🎯 确保测量工具栏显示
        forceToolbarState()

        // 确保有位图数据
        if (currentBitmap == null) {
            updateCurrentBitmap()
        }

        // 如果还是没有位图，创建一个默认的
        val bitmap = currentBitmap ?: run {
            Log.w("ImageDialog", "⚠️ No bitmap available, creating default")
            Bitmap.createBitmap(1000, 1000, Bitmap.Config.ARGB_8888)
        }

        try {
            if (!isMeasuringFourPointAngle) {
                // 🚀 第一次点击：启动四点角度测量模式
                Log.d("ImageDialog", "🎯 First click - starting four-point angle measurement mode...")

                // 🛡️ 设置测量模式保护（条件性）
                protectMeasurementMode()

                // 🚀 初始化MeasurementManager
                measurementManager.initialize(bitmap)

                // 🎯 开始四点角度测量模式
                if (measurementManager.startFourPointAngleMeasurement()) {
                    isMeasuringFourPointAngle = true // 🔄 同步Fragment状态
                    Log.d("ImageDialog", "✅ Four-point angle measurement mode started - first angle created, isMeasuringFourPointAngle=$isMeasuringFourPointAngle")
                } else {
                    Log.e("ImageDialog", "❌ Failed to start four-point angle measurement")
                    // 如果启动失败，重置测量状态
                    isMeasuringFourPointAngle = false
                }
            } else {
                // 🎯 后续点击：在测量模式下添加新四点角度
                Log.d("ImageDialog", "🎯 Subsequent click - adding new four-point angle in measurement mode...")

                if (measurementManager.addNewFourPointAngleMeasurement()) {
                    Log.d("ImageDialog", "✅ New four-point angle added successfully")
                } else {
                    Log.e("ImageDialog", "❌ Failed to add new four-point angle")
                }
            }

        } catch (e: Exception) {
            Log.e("ImageDialog", "❌ Failed to handle four-point angle measurement: ${e.message}")
            e.printStackTrace()
        }
    }

    /**
     * 🎯 开始四点角度测量 - 混合模式支持
     */
    fun startFourPointAngleMeasurementMixed() {
        Log.d("ImageDialog", "🎯 Four-point angle measurement button clicked - mixed mode - current state: isMeasuringAngle=$isMeasuringAngle, isMeasuringFourPointAngle=$isMeasuringFourPointAngle")

        // 🎯 确保测量工具栏显示
        forceToolbarState()

        // 确保有位图数据
        if (currentBitmap == null) {
            updateCurrentBitmap()
        }

        // 如果还是没有位图，创建一个默认的
        val bitmap = currentBitmap ?: run {
            Log.w("ImageDialog", "⚠️ No bitmap available, creating default")
            Bitmap.createBitmap(1000, 1000, Bitmap.Config.ARGB_8888)
        }

        try {
            if (!isMeasuringFourPointAngle) {
                // 🔄 混合共存模式：如果三点角度测量存在，将其设为未激活状态
                if (isMeasuringAngle) {
                    Log.d("ImageDialog", "🔄 Deactivating angle measurement, keeping it on display")
                    measurementManager.deactivateAngleMeasurement()
                }

                // 🚀 第一次点击：启动四点角度测量模式
                Log.d("ImageDialog", "🎯 First click - starting four-point angle measurement mode...")

                // 🛡️ 设置测量模式保护（条件性）
                protectMeasurementMode()

                // 🚀 初始化MeasurementManager
                measurementManager.initialize(bitmap)

                // 🎯 开始四点角度测量模式
                if (measurementManager.startFourPointAngleMeasurement()) {
                    isMeasuringFourPointAngle = true // 🔄 同步Fragment状态
                    Log.d("ImageDialog", "✅ Four-point angle measurement mode started - first angle created, isMeasuringFourPointAngle=$isMeasuringFourPointAngle")
                } else {
                    Log.e("ImageDialog", "❌ Failed to start four-point angle measurement")
                    // 如果启动失败，重置测量状态
                    isMeasuringFourPointAngle = false
                }
            } else {
                // 🎯 后续点击：在测量模式下添加新四点角度
                Log.d("ImageDialog", "🎯 Subsequent click - adding new four-point angle in measurement mode...")

                if (measurementManager.addNewFourPointAngleMeasurement()) {
                    Log.d("ImageDialog", "✅ New four-point angle added successfully")
                } else {
                    Log.e("ImageDialog", "❌ Failed to add new four-point angle")
                }
            }

        } catch (e: Exception) {
            Log.e("ImageDialog", "❌ Failed to handle four-point angle measurement: ${e.message}")
            e.printStackTrace()
        }
    }

    /**
     * 🎯 开始点测量 - 与角度测量保持一致的架构
     */
    fun startPointMeasurement() {
        Log.d("ImageDialog", "🎯 Point measurement button clicked - current state: isMeasuringAngle=$isMeasuringAngle, isMeasuringFourPointAngle=$isMeasuringFourPointAngle, isMeasuringPoint=$isMeasuringPoint")

        // 🎯 确保测量工具栏显示
        forceToolbarState()

        // 确保有位图数据
        val bitmap = currentBitmap ?: run {
            Log.w("ImageDialog", "⚠️ No bitmap available for point measurement")
            return
        }

        try {
            if (!isMeasuringPoint) {
                // 🔄 混合共存模式：如果其他测量存在，将其设为未激活状态
                if (isMeasuringAngle) {
                    Log.d("ImageDialog", "🔄 Deactivating angle measurement, keeping it on display")
                    measurementManager.deactivateAngleMeasurement()
                }
                if (isMeasuringFourPointAngle) {
                    Log.d("ImageDialog", "🔄 Deactivating four-point angle measurement, keeping it on display")
                    measurementManager.deactivateFourPointAngleMeasurement()
                }

                // 🚀 第一次点击：启动点测量模式
                Log.d("ImageDialog", "🎯 First click - starting point measurement mode...")

                // 🛡️ 设置测量模式保护（条件性）
                protectMeasurementMode()

                // 🚀 初始化MeasurementManager
                measurementManager.initialize(bitmap)

                // 🎯 开始点测量模式
                if (measurementManager.startPointMeasurement()) {
                    isMeasuringPoint = true // 🔄 同步Fragment状态
                    Log.d("ImageDialog", "✅ Point measurement mode started - first point created, isMeasuringPoint=$isMeasuringPoint")
                } else {
                    Log.e("ImageDialog", "❌ Failed to start point measurement")
                    // 如果启动失败，重置测量状态
                    isMeasuringPoint = false
                }
            } else {
                // 🎯 后续点击：在测量模式下添加新点
                Log.d("ImageDialog", "🎯 Subsequent click - adding new point in measurement mode...")

                // 直接添加新的点测量
                measurementManager.addNewPointMeasurement()
                Log.d("ImageDialog", "✅ New point added in existing measurement mode")
            }

        } catch (e: Exception) {
            Log.e("ImageDialog", "❌ Failed to handle point measurement: ${e.message}")
            e.printStackTrace()
        }
    }

    /**
     * ⏹️ 停止点测量
     */
    fun stopPointMeasurement() {
        if (!isMeasuringPoint) return

        Log.d("ImageDialog", "⏹️ Stopping point measurement...")

        try {
            // ⏹️ 停止点测量
            measurementManager.stopPointMeasurement()
            isMeasuringPoint = false

            Log.d("ImageDialog", "✅ Point measurement stopped - UI remains interactive")

        } catch (e: Exception) {
            Log.e("ImageDialog", "❌ Failed to stop point measurement: ${e.message}")
            e.printStackTrace()
        }
    }

    /**
     * 📏 开始线段测量 - 与其他测量保持一致的架构
     */
    private fun startLineMeasurement() {
        Log.d("ImageDialog", "📏 Line measurement button clicked - current state: isMeasuringAngle=$isMeasuringAngle, isMeasuringFourPointAngle=$isMeasuringFourPointAngle, isMeasuringPoint=$isMeasuringPoint, isMeasuringLine=$isMeasuringLine")

        // 🎯 确保测量工具栏显示
        forceToolbarState()

        // 确保有位图数据
        val bitmap = currentBitmap ?: run {
            Log.w("ImageDialog", "⚠️ No bitmap available for line measurement")
            return
        }

        try {
            if (!isMeasuringLine) {
                // 🔄 混合共存模式：如果其他测量存在，将其设为未激活状态
                if (isMeasuringAngle) {
                    Log.d("ImageDialog", "🔄 Deactivating angle measurement, keeping it on display")
                    measurementManager.deactivateAngleMeasurement()
                }
                if (isMeasuringFourPointAngle) {
                    Log.d("ImageDialog", "🔄 Deactivating four-point angle measurement, keeping it on display")
                    measurementManager.deactivateFourPointAngleMeasurement()
                }
                if (isMeasuringPoint) {
                    Log.d("ImageDialog", "🔄 Deactivating point measurement, keeping it on display")
                    measurementManager.deactivatePointMeasurement()
                }

                // 🚀 第一次点击：启动线段测量模式
                Log.d("ImageDialog", "📏 First click - starting line measurement mode...")

                // 🛡️ 设置测量模式保护（条件性）
                protectMeasurementMode()

                // 🚀 初始化MeasurementManager
                measurementManager.initialize(bitmap)

                // 📏 启动线段测量
                if (measurementManager.startLineMeasurement()) {
                    isMeasuringLine = true // 🔄 同步Fragment状态
                    Log.d("ImageDialog", "✅ Line measurement mode started - first line created, isMeasuringLine=$isMeasuringLine")
                } else {
                    Log.e("ImageDialog", "❌ Failed to start line measurement")
                    // 如果启动失败，重置测量状态
                    isMeasuringLine = false
                }
            } else {
                // 🎯 后续点击：在测量模式下添加新线段
                Log.d("ImageDialog", "📏 Subsequent click - adding new line in measurement mode...")

                // 直接添加新的线段测量
                measurementManager.addNewLineMeasurement()
                Log.d("ImageDialog", "✅ New line added in existing measurement mode")
            }

        } catch (e: Exception) {
            Log.e("ImageDialog", "❌ Failed to handle line measurement: ${e.message}")
            e.printStackTrace()
            isMeasuringLine = false
        }
    }

    /**
     * 🛑 停止线段测量
     */
    fun stopLineMeasurement() {
        if (!isMeasuringLine) return

        Log.d("ImageDialog", "🛑 Stopping line measurement...")

        try {
            // 🛑 停止线段测量
            measurementManager.stopLineMeasurement()
            isMeasuringLine = false

            Log.d("ImageDialog", "✅ Line measurement stopped - UI remains interactive")

        } catch (e: Exception) {
            Log.e("ImageDialog", "❌ Failed to stop line measurement: ${e.message}")
            e.printStackTrace()
        }
    }

    /**
     * ⏹️ 停止四点角度测量
     */
    fun stopFourPointAngleMeasurement() {
        if (!isMeasuringFourPointAngle) return

        Log.d("ImageDialog", "⏹️ Stopping four-point angle measurement...")

        try {
            // ⏹️ 停止四点角度测量
            measurementManager.stopFourPointAngleMeasurement()
            isMeasuringFourPointAngle = false

            Log.d("ImageDialog", "✅ Four-point angle measurement stopped - UI remains interactive")

        } catch (e: Exception) {
            Log.e("ImageDialog", "❌ Failed to stop four-point angle measurement: ${e.message}")
            e.printStackTrace()
        }
    }

    /**
     * ⏹️ 停止角度测量 - 极简API调用，解锁UI状态
     */
    fun stopAngleMeasurement() {
        if (!isMeasuringAngle) return

        Log.d("ImageDialog", "⏹️ Stopping angle measurement...")

        try {
            // ⏹️ 停止角度测量 - 一行代码搞定！
            measurementManager.stopAngleMeasurement()
            isMeasuringAngle = false

            // ✅ 单击监听器保持启用，无需重新设置
            // 移除不必要的监听器恢复操作

            Log.d("ImageDialog", "✅ Angle measurement stopped - UI remains interactive")

        } catch (e: Exception) {
            Log.e("ImageDialog", "❌ Failed to stop angle measurement: ${e.message}")
            e.printStackTrace()
        }
    }

    /**
     * 🗑️ 删除选中的测量
     */
    fun deleteSelectedMeasurement() {
        Log.d("ImageDialog", "🗑️ deleteSelectedMeasurement called - isMeasuringAngle=$isMeasuringAngle, isMeasuringFourPointAngle=$isMeasuringFourPointAngle, isMeasuringPoint=$isMeasuringPoint, isMeasuringLine=$isMeasuringLine")

        if (!isMeasuringAngle && !isMeasuringFourPointAngle && !isMeasuringPoint && !isMeasuringLine) {
            Log.w("ImageDialog", "⚠️ Not in any measurement mode - cannot delete")
            return
        }

        Log.d("ImageDialog", "🗑️ Deleting selected measurement...")

        try {
            val result = measurementManager.deleteSelectedMeasurement()
            Log.d("ImageDialog", "🗑️ MeasurementManager.deleteSelectedMeasurement() returned: $result")

            if (result) {
                Log.d("ImageDialog", "✅ Selected measurement deleted successfully")
            } else {
                Log.w("ImageDialog", "⚠️ No selected measurement to delete")
            }

        } catch (e: Exception) {
            Log.e("ImageDialog", "❌ Failed to delete selected measurement: ${e.message}")
            e.printStackTrace()
        }
    }

    // updateAngleMeasurementOverlay方法已被MeasurementManager内部处理，不再需要

    /**
     * 🧹 清理测量资源
     */
    override fun onDestroyView() {
        super.onDestroyView()

        try {
            if (::measurementManager.isInitialized) {
                measurementManager.cleanup()
            }
            Log.d("ImageDialog", "🧹 Measurement resources cleaned up")
        } catch (e: Exception) {
            Log.e("ImageDialog", "❌ Error cleaning up measurement resources: ${e.message}")
        }
    }

    /**
     * 🖼️ 加载图片时更新位图引用
     */
    private fun updateCurrentBitmap() {
        // 从ImageView的drawable获取位图
        val drawable = binding.imageView.drawable
        if (drawable != null) {
            try {
                // 创建位图副本用于测量
                val bitmap = Bitmap.createBitmap(
                    drawable.intrinsicWidth,
                    drawable.intrinsicHeight,
                    Bitmap.Config.ARGB_8888
                )
                val canvas = android.graphics.Canvas(bitmap)
                drawable.setBounds(0, 0, canvas.width, canvas.height)
                drawable.draw(canvas)

                currentBitmap = bitmap
                Log.d("ImageDialog", "🖼️ Updated current bitmap: ${bitmap.width}x${bitmap.height}")
            } catch (e: Exception) {
                Log.e("ImageDialog", "❌ Failed to create bitmap from drawable: ${e.message}")
                // 创建一个默认的小位图作为备用
                currentBitmap = Bitmap.createBitmap(100, 100, Bitmap.Config.ARGB_8888)
            }
        } else {
            Log.w("ImageDialog", "⚠️ No drawable available for bitmap creation")
            // 创建一个默认的小位图作为备用
            currentBitmap = Bitmap.createBitmap(100, 100, Bitmap.Config.ARGB_8888)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        hideMeasurementToolbar()

        // 清理测量资源
        if (isMeasuringAngle) {
            stopAngleMeasurement()
        }
        if (isMeasuringFourPointAngle) {
            stopFourPointAngleMeasurement()
        }
        if (isMeasuringPoint) {
            stopPointMeasurement()
        }
        if (isMeasuringLine) {
            stopLineMeasurement()
        }
    }
}
