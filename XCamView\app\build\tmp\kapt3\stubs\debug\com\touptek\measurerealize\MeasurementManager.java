package com.touptek.measurerealize;

import java.lang.System;

/**
 * 🎨 专业级测量管理器 - 高度封装的测量功能管理类
 *
 * 核心职责：
 * 1. 管理测量状态和生命周期
 * 2. 封装TpImageView与测量功能的集成
 * 3. 提供极简的API接口给上层调用
 * 4. 处理所有复杂的交互逻辑
 *
 * 设计理念：让上层调用者只需要关心开始/停止测量，所有细节都由Manager处理
 */
@kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000`\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u0002\n\u0002\b$\u0018\u0000 F2\u00020\u0001:\u0002FGB)\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\t\u00a2\u0006\u0002\u0010\nJ\u0006\u0010\u001f\u001a\u00020\u0014J\u0006\u0010 \u001a\u00020\u0014J\u0006\u0010!\u001a\u00020\u0014J\u0006\u0010\"\u001a\u00020\u0014J\u0006\u0010#\u001a\u00020$J\u0010\u0010%\u001a\u00020$2\u0006\u0010&\u001a\u00020\fH\u0002J\u0006\u0010\'\u001a\u00020$J\u0006\u0010(\u001a\u00020$J\u0006\u0010)\u001a\u00020$J\u0006\u0010*\u001a\u00020\u0014J\b\u0010+\u001a\u00020$H\u0002J\b\u0010,\u001a\u0004\u0018\u00010\fJ\b\u0010-\u001a\u00020\u0014H\u0002J\u000e\u0010.\u001a\u00020$2\u0006\u0010/\u001a\u00020\u0010J\u000e\u00100\u001a\u00020\u00142\u0006\u00101\u001a\u00020\fJ\u0006\u0010\u0013\u001a\u00020\u0014J\u0006\u00102\u001a\u00020\u0014J\u0006\u0010\u0015\u001a\u00020\u0014J\u0006\u00103\u001a\u00020\u0014J\u0010\u00104\u001a\u00020$2\u0006\u00101\u001a\u00020\fH\u0002J\b\u00105\u001a\u00020$H\u0002J\b\u00106\u001a\u00020$H\u0002J\b\u00107\u001a\u00020$H\u0002J\u0006\u00108\u001a\u00020\u0014J\u0006\u00109\u001a\u00020\u0014J\u0006\u0010:\u001a\u00020\u0014J\u0006\u0010;\u001a\u00020\u0014J\u0006\u0010<\u001a\u00020$J\u0006\u0010=\u001a\u00020$J\u0006\u0010>\u001a\u00020$J\u0006\u0010?\u001a\u00020$J\b\u0010@\u001a\u00020$H\u0002J\b\u0010A\u001a\u00020$H\u0002J\b\u0010B\u001a\u00020$H\u0002J\b\u0010C\u001a\u00020$H\u0002J\b\u0010D\u001a\u00020$H\u0002J\b\u0010E\u001a\u00020\u0014H\u0002R\u0010\u0010\u000b\u001a\u0004\u0018\u00010\fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\u000eX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u000f\u001a\u0004\u0018\u00010\u0010X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0011\u001a\u00020\u0012X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0013\u001a\u00020\u0014X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0015\u001a\u00020\u0014X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0016\u001a\u00020\u0014X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0017\u001a\u00020\u0014X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0018\u001a\u00020\u0014X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0019\u001a\u00020\u001aX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001b\u001a\u00020\u001cX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\b\u001a\u0004\u0018\u00010\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001d\u001a\u00020\u001eX\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006H"}, d2 = {"Lcom/touptek/measurerealize/MeasurementManager;", "", "context", "Landroid/content/Context;", "imageView", "Lcom/touptek/measurerealize/TpImageView;", "overlayView", "Lcom/touptek/measurerealize/utils/MeasurementOverlayView;", "statusTextView", "Landroid/widget/TextView;", "(Landroid/content/Context;Lcom/touptek/measurerealize/TpImageView;Lcom/touptek/measurerealize/utils/MeasurementOverlayView;Landroid/widget/TextView;)V", "activeMeasurementMode", "Lcom/touptek/measurerealize/MeasurementManager$MeasurementMode;", "angleMeasureHelper", "Lcom/touptek/measurerealize/utils/AngleMeasureHelper;", "currentBitmap", "Landroid/graphics/Bitmap;", "fourPointAngleHelper", "Lcom/touptek/measurerealize/utils/FourPointAngleHelper;", "isAngleMeasuring", "", "isFourPointAngleMeasuring", "isInitialized", "isLineMeasuring", "isPointMeasuring", "lineMeasureHelper", "Lcom/touptek/measurerealize/utils/LineMeasureHelper;", "pointMeasureHelper", "Lcom/touptek/measurerealize/utils/PointMeasureHelper;", "touchHandler", "Lcom/touptek/measurerealize/MeasurementTouchHandler;", "addNewAngleMeasurement", "addNewFourPointAngleMeasurement", "addNewLineMeasurement", "addNewPointMeasurement", "cleanup", "", "clearOtherModeSelections", "activeMode", "deactivateAngleMeasurement", "deactivateFourPointAngleMeasurement", "deactivatePointMeasurement", "deleteSelectedMeasurement", "forceOverlayDisplay", "getActiveMeasurementMode", "hasValidTouchHandler", "initialize", "bitmap", "isActiveMeasurementMode", "mode", "isDraggingPoint", "isMixedMeasuring", "setActiveMode", "setupFourPointHybridTouchHandler", "setupHybridTouchHandler", "setupMixedTouchHandler", "startAngleMeasurement", "startFourPointAngleMeasurement", "startLineMeasurement", "startPointMeasurement", "stopAngleMeasurement", "stopFourPointAngleMeasurement", "stopLineMeasurement", "stopPointMeasurement", "updateFourPointOverlayDisplay", "updateLineMeasurementDisplay", "updateMixedOverlayDisplay", "updateOverlayDisplay", "updatePointMeasurementDisplay", "validateState", "Companion", "MeasurementMode", "app_debug"})
public final class MeasurementManager {
    private final android.content.Context context = null;
    private final com.touptek.measurerealize.TpImageView imageView = null;
    private final com.touptek.measurerealize.utils.MeasurementOverlayView overlayView = null;
    private final android.widget.TextView statusTextView = null;
    @org.jetbrains.annotations.NotNull
    public static final com.touptek.measurerealize.MeasurementManager.Companion Companion = null;
    private static final java.lang.String TAG = "MeasurementManager";
    private final com.touptek.measurerealize.utils.AngleMeasureHelper angleMeasureHelper = null;
    private final com.touptek.measurerealize.utils.FourPointAngleHelper fourPointAngleHelper = null;
    private final com.touptek.measurerealize.utils.PointMeasureHelper pointMeasureHelper = null;
    private final com.touptek.measurerealize.utils.LineMeasureHelper lineMeasureHelper = null;
    private final com.touptek.measurerealize.MeasurementTouchHandler touchHandler = null;
    private boolean isAngleMeasuring = false;
    private boolean isFourPointAngleMeasuring = false;
    private boolean isPointMeasuring = false;
    private boolean isLineMeasuring = false;
    private com.touptek.measurerealize.MeasurementManager.MeasurementMode activeMeasurementMode;
    private android.graphics.Bitmap currentBitmap;
    private boolean isInitialized = false;
    
    public MeasurementManager(@org.jetbrains.annotations.NotNull
    android.content.Context context, @org.jetbrains.annotations.NotNull
    com.touptek.measurerealize.TpImageView imageView, @org.jetbrains.annotations.NotNull
    com.touptek.measurerealize.utils.MeasurementOverlayView overlayView, @org.jetbrains.annotations.Nullable
    android.widget.TextView statusTextView) {
        super();
    }
    
    /**
     * 🚀 初始化测量管理器
     */
    public final void initialize(@org.jetbrains.annotations.NotNull
    android.graphics.Bitmap bitmap) {
    }
    
    /**
     * 🎯 开始角度测量 - 支持混合模式
     */
    public final boolean startAngleMeasurement() {
        return false;
    }
    
    /**
     * ⏹️ 停止角度测量 - 极简API
     */
    public final void stopAngleMeasurement() {
    }
    
    /**
     * 🎯 开始四点角度测量 - 支持混合模式
     */
    public final boolean startFourPointAngleMeasurement() {
        return false;
    }
    
    /**
     * ⏹️ 停止四点角度测量
     */
    public final void stopFourPointAngleMeasurement() {
    }
    
    /**
     * 🎯 开始点测量 - 支持混合模式
     */
    public final boolean startPointMeasurement() {
        return false;
    }
    
    /**
     * 🛑 停止点测量
     */
    public final void stopPointMeasurement() {
    }
    
    /**
     * 📏 开始线段测量 - 极简API
     */
    public final boolean startLineMeasurement() {
        return false;
    }
    
    /**
     * 🛑 停止线段测量
     */
    public final void stopLineMeasurement() {
    }
    
    /**
     * 🎯 强制覆盖层显示 - 复制yolo_demo的成功机制
     */
    private final void forceOverlayDisplay() {
    }
    
    /**
     * 🔄 更新覆盖层显示 - 支持多角度同时显示
     */
    private final void updateOverlayDisplay() {
    }
    
    /**
     * 🔄 更新点测量覆盖层显示
     */
    private final void updatePointMeasurementDisplay() {
    }
    
    /**
     * 📏 更新线段测量覆盖层显示
     */
    private final void updateLineMeasurementDisplay() {
    }
    
    /**
     * 🔄 更新四点角度覆盖层显示
     */
    private final void updateFourPointOverlayDisplay() {
    }
    
    /**
     * 🔄 更新混合覆盖层显示 - 基于数据存在性显示，支持数据持久化
     */
    private final void updateMixedOverlayDisplay() {
    }
    
    /**
     * 🚀 设置四点角度混合触摸处理器
     */
    private final void setupFourPointHybridTouchHandler() {
    }
    
    /**
     * 🚀 设置混合触摸处理器（支持缩放+测量）
     */
    private final void setupHybridTouchHandler() {
    }
    
    /**
     * 🚀 设置混合模式触摸处理器 - 智能激活对应的测量类型
     */
    private final void setupMixedTouchHandler() {
    }
    
    /**
     * 🔍 检查是否有有效的触摸处理器
     */
    private final boolean hasValidTouchHandler() {
        return false;
    }
    
    /**
     * 📊 状态验证方法 - 确保状态一致性（支持混合共存模式）
     */
    private final boolean validateState() {
        return false;
    }
    
    /**
     * 📊 获取当前测量状态
     */
    public final boolean isAngleMeasuring() {
        return false;
    }
    
    /**
     * 📊 获取四点角度测量状态
     */
    public final boolean isFourPointAngleMeasuring() {
        return false;
    }
    
    /**
     * 📊 检查是否在任何测量模式下（混合模式支持）
     */
    public final boolean isMixedMeasuring() {
        return false;
    }
    
    /**
     * 🔄 设置激活的测量模式
     */
    private final void setActiveMode(com.touptek.measurerealize.MeasurementManager.MeasurementMode mode) {
    }
    
    /**
     * 🔄 取消激活三点角度测量（保持数据，仅改变激活状态）
     */
    public final void deactivateAngleMeasurement() {
    }
    
    /**
     * 🔄 取消激活四点角度测量（保持数据，仅改变激活状态）
     */
    public final void deactivateFourPointAngleMeasurement() {
    }
    
    /**
     * � 取消激活点测量（保持数据，仅改变激活状态）
     */
    public final void deactivatePointMeasurement() {
    }
    
    /**
     * �📊 获取当前激活的测量模式
     */
    @org.jetbrains.annotations.Nullable
    public final com.touptek.measurerealize.MeasurementManager.MeasurementMode getActiveMeasurementMode() {
        return null;
    }
    
    /**
     * 📊 检查指定模式是否为激活状态
     */
    public final boolean isActiveMeasurementMode(@org.jetbrains.annotations.NotNull
    com.touptek.measurerealize.MeasurementManager.MeasurementMode mode) {
        return false;
    }
    
    /**
     * 🔄 清除其他模式的选中状态（避免多模式选中冲突）
     */
    private final void clearOtherModeSelections(com.touptek.measurerealize.MeasurementManager.MeasurementMode activeMode) {
    }
    
    /**
     * 🔍 检查是否正在拖拽测量点 - 支持所有测量类型
     */
    public final boolean isDraggingPoint() {
        return false;
    }
    
    /**
     * 🎯 添加新的角度测量 - 在测量模式下生成新角度
     */
    public final boolean addNewAngleMeasurement() {
        return false;
    }
    
    /**
     * 🎯 添加新的点测量 - 在测量模式下生成新点
     */
    public final boolean addNewPointMeasurement() {
        return false;
    }
    
    /**
     * 🎯 添加新的四点角度测量 - 在测量模式下生成新四点角度
     */
    public final boolean addNewFourPointAngleMeasurement() {
        return false;
    }
    
    /**
     * 📏 添加新的线段测量 - 在测量模式下生成新线段
     */
    public final boolean addNewLineMeasurement() {
        return false;
    }
    
    /**
     * 🗑️ 删除选中的测量 - 支持三点和四点角度测量
     */
    public final boolean deleteSelectedMeasurement() {
        return false;
    }
    
    /**
     * 🧹 清理资源
     */
    public final void cleanup() {
    }
    
    /**
     * 测量模式枚举
     */
    @kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\u0006\b\u0086\u0001\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004j\u0002\b\u0005j\u0002\b\u0006\u00a8\u0006\u0007"}, d2 = {"Lcom/touptek/measurerealize/MeasurementManager$MeasurementMode;", "", "(Ljava/lang/String;I)V", "ANGLE", "FOUR_POINT_ANGLE", "POINT", "LINE", "app_debug"})
    public static enum MeasurementMode {
        /*public static final*/ ANGLE /* = new ANGLE() */,
        /*public static final*/ FOUR_POINT_ANGLE /* = new FOUR_POINT_ANGLE() */,
        /*public static final*/ POINT /* = new POINT() */,
        /*public static final*/ LINE /* = new LINE() */;
        
        MeasurementMode() {
        }
    }
    
    @kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0005"}, d2 = {"Lcom/touptek/measurerealize/MeasurementManager$Companion;", "", "()V", "TAG", "", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}