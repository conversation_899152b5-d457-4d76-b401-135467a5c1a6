package com.touptek.measurerealize.utils

import android.graphics.Bitmap
import android.graphics.Matrix
import android.graphics.PointF
import android.util.Log
import android.view.MotionEvent
import android.widget.ImageView

import java.util.*
import kotlin.math.*

/**
 * 📏 专业级线段测量助手 - 双端点线段测量系统
 * 
 * 🎯 核心功能：
 * - 双坐标系统支持（视图坐标 + 位图坐标）
 * - 专业级触摸交互（拖拽、选择、长按删除）
 * - 实时长度计算和显示
 * - 缩放跟随和坐标同步
 * - 混合模式兼容
 */

/**
 * 📏 线段测量实例 - 专业级数据结构
 */
data class LineMeasurement(
    val id: String = UUID.randomUUID().toString(),
    var viewPoints: MutableList<PointF> = mutableListOf(),  // [起点, 终点] 视图坐标
    var bitmapPoints: MutableList<PointF> = mutableListOf(), // [起点, 终点] 位图坐标
    var isSelected: Boolean = false,
    var isEditing: Boolean = false,
    var isCompleted: Boolean = false,
    var textPosition: PointF? = null,    // 长度文本位置（中点上方）
    var creationTime: Long = System.currentTimeMillis(),
    var lastModified: Long = System.currentTimeMillis()
) {
    /**
     * 📏 计算线段长度（使用位图坐标 - 真实长度，不受缩放影响）
     */
    fun calculateLength(): Double {
        if (bitmapPoints.size < 2) return 0.0
        val p1 = bitmapPoints[0]
        val p2 = bitmapPoints[1]
        return sqrt((p2.x - p1.x).pow(2) + (p2.y - p1.y).pow(2)).toDouble()
    }

    /**
     * 🔄 同步位图坐标
     */
    fun syncBitmapCoords(imageView: ImageView) {
        bitmapPoints.clear()
        viewPoints.forEach { viewPoint ->
            val bitmapPoint = convertViewToBitmapCoords(viewPoint, imageView)
            bitmapPoints.add(bitmapPoint)
        }
    }

    /**
     * 🔄 同步视图坐标（缩放变化时调用） - 与AngleMeasureHelper保持一致
     */
    fun syncViewCoords(imageView: ImageView) {
        viewPoints.clear()
        bitmapPoints.forEach { bitmapPoint ->
            val viewPoint = convertBitmapToViewCoords(bitmapPoint, imageView)
            viewPoints.add(viewPoint)
        }
    }

    /**
     * 🎯 视图坐标转位图坐标
     */
    private fun convertViewToBitmapCoords(viewPoint: PointF, imageView: ImageView): PointF {
        val matrix = imageView.imageMatrix
        val inverseMatrix = Matrix()

        return if (matrix.invert(inverseMatrix)) {
            val point = FloatArray(2) { 0f }
            point[0] = viewPoint.x
            point[1] = viewPoint.y
            inverseMatrix.mapPoints(point)
            PointF(point[0], point[1])
        } else {
            PointF(viewPoint.x, viewPoint.y)
        }
    }

    /**
     * 🎯 位图坐标转视图坐标
     */
    private fun convertBitmapToViewCoords(bitmapPoint: PointF, imageView: ImageView): PointF {
        val matrix = imageView.imageMatrix
        val point = FloatArray(2) { 0f }
        point[0] = bitmapPoint.x
        point[1] = bitmapPoint.y
        matrix.mapPoints(point)
        return PointF(point[0], point[1])
    }

    /**
     * � 检查触摸点是否在端点范围内
     */
    fun isPointInTouchRange(touchPoint: PointF, radius: Float): Boolean {
        return viewPoints.any { point ->
            val distance = sqrt((touchPoint.x - point.x).pow(2) + (touchPoint.y - point.y).pow(2))
            distance <= radius
        }
    }

    /**
     * 🎯 获取最近的端点索引
     */
    fun getNearestPointIndex(touchPoint: PointF): Int {
        if (viewPoints.isEmpty()) return -1
        
        var nearestIndex = 0
        var minDistance = Float.MAX_VALUE
        
        viewPoints.forEachIndexed { index, point ->
            val distance = sqrt((touchPoint.x - point.x).pow(2) + (touchPoint.y - point.y).pow(2))
            if (distance < minDistance) {
                minDistance = distance
                nearestIndex = index
            }
        }
        
        return nearestIndex
    }

    /**
     * 🎯 更新文本位置（线段中点上方）
     */
    fun updateTextPosition() {
        if (viewPoints.size >= 2) {
            val midX = (viewPoints[0].x + viewPoints[1].x) / 2
            val midY = (viewPoints[0].y + viewPoints[1].y) / 2
            textPosition = PointF(midX, midY - 30f) // 中点上方30像素
        }
    }
}

/**
 * 📏 线段测量助手 - 专业级测量管理系统
 */
class LineMeasureHelper {
    companion object {
        private const val TAG = "LineMeasureHelper"
        private const val TOUCH_RADIUS = 80f           // 触摸检测半径
        private const val CLICK_TOLERANCE = 20f        // 点击容差
        private const val LONG_PRESS_DURATION = 800L   // 长按删除时间
        private const val DEFAULT_LINE_LENGTH = 100f   // 默认线段长度
    }

    // 🎯 核心数据
    private lateinit var imageView: ImageView
    private lateinit var originalBitmap: Bitmap
    private val measurements = mutableListOf<LineMeasurement>()
    
    // 🎯 状态管理
    private var selectedMeasurement: LineMeasurement? = null
    private var activeMeasurement: LineMeasurement? = null
    private var isDraggingPoint = false
    private var draggedPointIndex = -1
    private var lastTouchTime = 0L
    private var longPressRunnable: Runnable? = null

    // 🎯 触摸事件状态 - 与AngleMeasureHelper保持一致
    private var longPressStartTime = 0L
    private var lastTouchX = 0f
    private var lastTouchY = 0f
    
    // 🎯 回调
    private var measurementUpdateCallback: (() -> Unit)? = null

    /**
     * 🚀 初始化助手
     */
    fun init(imageView: ImageView, bitmap: Bitmap) {
        this.imageView = imageView
        this.originalBitmap = bitmap.copy(Bitmap.Config.ARGB_8888, true)
        
        Log.d(TAG, "🚀 LineMeasureHelper initialized")
    }

    /**
     * 🔄 设置测量更新回调
     */
    fun setMeasurementUpdateCallback(callback: () -> Unit) {
        this.measurementUpdateCallback = callback
    }

    /**
     * 🔔 通知更新
     */
    private fun notifyUpdate() {
        measurementUpdateCallback?.invoke()
    }

    /**
     * 🎯 开始新的线段测量 - 在屏幕中心生成默认线段
     */
    fun startNewMeasurement(): String {
        if (!::originalBitmap.isInitialized) {
            Log.w(TAG, "Bitmap not initialized")
            return ""
        }

        // 在视图中心生成水平线段
        val viewCenterX = imageView.width / 2f
        val viewCenterY = imageView.height / 2f
        val startPoint = PointF(viewCenterX - DEFAULT_LINE_LENGTH / 2, viewCenterY)
        val endPoint = PointF(viewCenterX + DEFAULT_LINE_LENGTH / 2, viewCenterY)

        // 创建测量实例
        val measurement = LineMeasurement(
            isSelected = true,
            isEditing = false,
            isCompleted = true
        )

        // 添加视图坐标点
        measurement.viewPoints.addAll(listOf(startPoint, endPoint))

        // 同步位图坐标
        measurement.syncBitmapCoords(imageView)

        // 更新文本位置
        measurement.updateTextPosition()

        // 取消其他测量的选中状态
        measurements.forEach {
            it.isSelected = false
            it.isEditing = false
        }

        measurements.add(measurement)
        selectedMeasurement = measurement
        activeMeasurement = measurement

        notifyUpdate()
        Log.d(TAG, "📏 Created new line measurement: ${measurement.calculateLength()} px")
        return measurement.id
    }

    /**
     * 🎯 处理触摸事件 - 与AngleMeasureHelper保持一致的交互逻辑
     */
    fun handleTouchEvent(event: MotionEvent, viewWidth: Int, viewHeight: Int): Boolean {
        val x = event.x
        val y = event.y
        val touchPoint = PointF(x, y)

        Log.d(TAG, "🎯 Touch event: action=${event.action}, point=($x, $y), isDragging=$isDraggingPoint")

        when (event.action) {
            MotionEvent.ACTION_DOWN -> {
                longPressStartTime = System.currentTimeMillis()
                // 记录触摸起始位置
                lastTouchX = x
                lastTouchY = y

                // 检查是否点击了现有测量的端点
                for (measurement in measurements.reversed()) {
                    if (measurement.isPointInTouchRange(touchPoint, TOUCH_RADIUS)) {
                        selectedMeasurement = measurement
                        draggedPointIndex = measurement.getNearestPointIndex(touchPoint)
                        isDraggingPoint = true

                        // 设置选中状态
                        measurements.forEach { it.isSelected = false }
                        measurement.isSelected = true

                        Log.d(TAG, "🎯 Started dragging point $draggedPointIndex of measurement ${measurement.id}")
                        notifyUpdate()
                        return true
                    }
                }

                // 🔧 智能空白区域检测 - 避免UI按钮区域被误判 - 与PointMeasureHelper保持一致
                if (isInImageContentArea(touchPoint, viewWidth, viewHeight)) {
                    measurements.forEach { it.isSelected = false }
                    selectedMeasurement = null
                    notifyUpdate()
                    Log.d(TAG, "� Clicked empty area in image content, deselected all measurements")
                } else {
                    Log.d(TAG, "🛡️ Clicked in UI area, preserving selection state")
                }

                return false // 让TpImageView处理缩放
            }

            MotionEvent.ACTION_MOVE -> {
                Log.d(TAG, "📱 ACTION_MOVE: isDragging=$isDraggingPoint, selected=${selectedMeasurement?.id}, dragIndex=$draggedPointIndex")

                if (isDraggingPoint && selectedMeasurement != null && draggedPointIndex >= 0) {
                    // 正常拖拽端点
                    selectedMeasurement!!.viewPoints[draggedPointIndex] = PointF(touchPoint.x, touchPoint.y)
                    selectedMeasurement!!.syncBitmapCoords(imageView)
                    selectedMeasurement!!.updateTextPosition()

                    Log.d(TAG, "✅ Point updated to ($x, $y)")
                    notifyUpdate()
                    return true
                } else if (!isDraggingPoint && selectedMeasurement != null) {
                    // 触摸事件恢复机制：尝试重新建立拖拽关系
                    Log.d(TAG, "🔄 Attempting touch recovery - checking if point is near measurement")

                    for (measurement in measurements.reversed()) {
                        if (measurement.isPointInTouchRange(touchPoint, TOUCH_RADIUS) && measurement == selectedMeasurement) {
                            // 重新建立拖拽关系
                            draggedPointIndex = measurement.getNearestPointIndex(touchPoint)
                            isDraggingPoint = true

                            Log.d(TAG, "🔄 Touch recovery successful - resumed dragging point $draggedPointIndex")

                            // 立即处理这次移动
                            selectedMeasurement!!.viewPoints[draggedPointIndex] = PointF(touchPoint.x, touchPoint.y)
                            selectedMeasurement!!.syncBitmapCoords(imageView)
                            selectedMeasurement!!.updateTextPosition()
                            notifyUpdate()
                            return true
                        }
                    }
                }

                Log.d(TAG, "❌ ACTION_MOVE not handled - no recovery possible")
                return false
            }

            MotionEvent.ACTION_UP -> {
                val currentTime = System.currentTimeMillis()
                val touchDuration = currentTime - longPressStartTime
                val touchDistance = sqrt((x - lastTouchX) * (x - lastTouchX) + (y - lastTouchY) * (y - lastTouchY))
                val wasDragging = isDraggingPoint

                Log.d(TAG, "🔚 ACTION_UP: wasDragging=$wasDragging, duration=$touchDuration, distance=$touchDistance")

                var handled = false

                // 重置拖拽状态
                isDraggingPoint = false
                draggedPointIndex = -1

                if (wasDragging) {
                    // 正常的拖拽结束 - 保持所有数据
                    Log.d(TAG, "✅ Normal drag completed - preserving measurement data")
                    notifyUpdate()
                    handled = true
                }

                // 长按删除逻辑 - 与AngleMeasureHelper保持一致
                if (!wasDragging && touchDistance < CLICK_TOLERANCE && touchDuration > LONG_PRESS_DURATION) {
                    // 查找被长按的测量点
                    for (measurement in measurements.reversed()) {
                        if (measurement.isPointInTouchRange(touchPoint, TOUCH_RADIUS)) {
                            measurements.remove(measurement)
                            Log.d(TAG, "🗑️ Long-press: Deleted measurement ${measurement.id}")
                            if (selectedMeasurement == measurement) {
                                selectedMeasurement = null
                            }
                            notifyUpdate()
                            handled = true
                            break
                        }
                    }
                }

                // 轻触选中逻辑 - 与AngleMeasureHelper保持一致
                if (!wasDragging && !handled && touchDistance < CLICK_TOLERANCE && touchDuration < LONG_PRESS_DURATION) {
                    // 检查是否点击了测量点
                    for (measurement in measurements.reversed()) {
                        if (measurement.isPointInTouchRange(touchPoint, TOUCH_RADIUS)) {
                            // 用户轻触了测量点，设置选中状态
                            measurements.forEach { it.isSelected = false }
                            measurement.isSelected = true
                            selectedMeasurement = measurement
                            Log.d(TAG, "🎯 ACTION_UP: Light touch selection - selected measurement ${measurement.id}")
                            notifyUpdate()
                            return true
                        }
                    }
                }

                return handled
            }
        }
        return false
    }







    /**
     * 🗑️ 删除测量
     */
    private fun deleteMeasurement(measurement: LineMeasurement) {
        measurements.remove(measurement)
        if (selectedMeasurement == measurement) {
            selectedMeasurement = null
        }
        if (activeMeasurement == measurement) {
            activeMeasurement = null
        }
        resetInteractionState()
        notifyUpdate()
        Log.d(TAG, "🗑️ Deleted line measurement: ${measurement.id}")
    }

    /**
     * 🔄 重置交互状态
     */
    private fun resetInteractionState() {
        isDraggingPoint = false
        draggedPointIndex = -1
        longPressStartTime = 0L
        lastTouchX = 0f
        lastTouchY = 0f
    }

    /**
     * 🎨 获取所有测量数据用于覆盖层显示
     */
    fun getAllMeasurementData(): List<LineMeasurementData> {
        return measurements.map { measurement ->
            LineMeasurementData(
                points = measurement.viewPoints.toList(),
                length = measurement.calculateLength(),
                isDragging = isDraggingPoint && measurement.isSelected,
                isSelected = measurement.isSelected
            )
        }
    }

    /**
     * 🎯 获取测量数量
     */
    fun getMeasurementCount(): Int = measurements.size

    /**
     * 🎯 检查是否有选中的测量
     */
    fun hasSelectedMeasurement(): Boolean {
        return measurements.any { it.isSelected }
    }

    /**
     * 🗑️ 删除选中的测量
     */
    fun deleteSelectedMeasurement(): Boolean {
        val selectedIndex = measurements.indexOfFirst { it.isSelected }
        return if (selectedIndex >= 0) {
            val deletedMeasurement = measurements.removeAt(selectedIndex)
            selectedMeasurement = null
            activeMeasurement = null
            resetInteractionState()
            notifyUpdate() // 🔔 通知更新显示
            Log.d(TAG, "🗑️ Deleted selected line measurement at index $selectedIndex: ${deletedMeasurement.id}")
            true
        } else {
            Log.w(TAG, "⚠️ No selected line measurement to delete")
            false
        }
    }

    /**
     * 🎯 添加新的线段测量 - 与AngleMeasureHelper保持一致
     */
    fun addNewMeasurement(): String {
        if (!::originalBitmap.isInitialized) {
            Log.w(TAG, "Bitmap not initialized")
            return ""
        }

        // 取消当前选中状态
        measurements.forEach { it.isSelected = false }

        // 创建新的线段测量
        val measurementId = startNewMeasurement()

        Log.d(TAG, "📏 Added new line measurement: $measurementId")
        return measurementId
    }

    /**
     * 🔄 处理缩放变化 - 同步所有测量的坐标
     */
    fun onScaleChanged() {
        Log.d(TAG, "🔄 Scale changed - syncing ${measurements.size} line measurements")
        // 从位图坐标恢复视图坐标，确保测量跟随图像
        measurements.forEach { measurement ->
            Log.d(TAG, "🔄 Before sync: viewPoints size=${measurement.viewPoints.size}")
            measurement.syncViewCoords(imageView)
            measurement.updateTextPosition() // 更新文本位置
            Log.d(TAG, "🔄 After sync: viewPoints updated for measurement ${measurement.id}")
        }
        notifyUpdate()
        Log.d(TAG, "🔄 Line coordinate sync completed")
    }

    /**
     * 🧹 清除所有选中状态
     */
    fun clearAllSelections() {
        measurements.forEach { it.isSelected = false }
        selectedMeasurement = null
        isDraggingPoint = false

        Log.d(TAG, "🔄 Cleared all selections in LineMeasureHelper")
        notifyUpdate()
    }

    /**
     * 🧹 清除所有测量
     */
    fun clearAllMeasurements() {
        measurements.clear()
        selectedMeasurement = null
        activeMeasurement = null
        resetInteractionState()
        notifyUpdate()
        Log.d(TAG, "🧹 Cleared all measurements")
    }

    /**
     * 🧹 重置助手
     */
    fun reset() {
        measurements.clear()
        selectedMeasurement = null
        activeMeasurement = null
        resetInteractionState()
        Log.d(TAG, "🧹 Helper reset")
    }

    /**
     * 🎯 检查触摸点是否在任何测量上（用于智能模式激活）
     */
    fun isPointOnMeasurement(touchPoint: PointF): Boolean {
        // 检查是否点击了现有测量的端点
        for (measurement in measurements.reversed()) {
            if (measurement.isPointInTouchRange(touchPoint, TOUCH_RADIUS)) {
                return true
            }
        }
        return false
    }

    /**
     * 🎯 检查是否正在拖拽点
     */
    fun isDraggingPoint(): Boolean = isDraggingPoint

    /**
     * 🎯 检查是否触摸到测量点
     */
    fun isTouchingMeasurementPoint(touchPoint: PointF): Boolean {
        return measurements.any { measurement ->
            measurement.isPointInTouchRange(touchPoint, TOUCH_RADIUS)
        }
    }

    /**
     * 🎯 检查是否靠近任何测量
     */
    fun isNearAnyMeasurement(touchPoint: PointF): Boolean {
        return measurements.any { measurement ->
            measurement.isPointInTouchRange(touchPoint, TOUCH_RADIUS)
        }
    }

    /**
     * 🛡️ 检测触摸点是否在图像内容区域（避免UI按钮区域） - 与PointMeasureHelper保持一致
     */
    private fun isInImageContentArea(touchPoint: PointF, viewWidth: Int, viewHeight: Int): Boolean {
        try {
            // 定义UI区域边界（顶部和底部各20%为UI区域）
            val topUIHeight = viewHeight * 0.2f
            val bottomUIStart = viewHeight * 0.8f

            // 如果触摸点在顶部或底部UI区域，不取消选中
            if (touchPoint.y < topUIHeight || touchPoint.y > bottomUIStart) {
                Log.d(TAG, "🛡️ Touch in UI area: y=${touchPoint.y}, topUI=$topUIHeight, bottomUI=$bottomUIStart")
                return false
            }

            // 中间区域认为是图像内容区域
            Log.d(TAG, "📍 Touch in image content area: y=${touchPoint.y}")
            return true

        } catch (e: Exception) {
            Log.e(TAG, "❌ Error checking image content area: ${e.message}")
            // 出错时保守处理，不取消选中
            return false
        }
    }

    /**
     * 🎯 清除所有选择状态
     */
    fun clearSelection() {
        measurements.forEach { measurement ->
            measurement.isSelected = false
        }
        selectedMeasurement = null
        Log.d(TAG, "🔄 Cleared all line measurement selections")
    }


}
