package com.touptek.measurerealize

import android.content.Context
import android.graphics.Bitmap
import android.util.Log
import android.view.MotionEvent
import android.view.View
import android.widget.TextView
import com.touptek.measurerealize.utils.AngleMeasureHelper
import com.touptek.measurerealize.utils.FourPointAngleHelper
import com.touptek.measurerealize.utils.MeasurementOverlayView
import com.touptek.measurerealize.TpImageView
import com.touptek.measurerealize.utils.PointMeasureHelper
import com.touptek.measurerealize.utils.LineMeasureHelper
import kotlin.math.cos

/**
 * 🎨 专业级测量管理器 - 高度封装的测量功能管理类
 * 
 * 核心职责：
 * 1. 管理测量状态和生命周期
 * 2. 封装TpImageView与测量功能的集成
 * 3. 提供极简的API接口给上层调用
 * 4. 处理所有复杂的交互逻辑
 * 
 * 设计理念：让上层调用者只需要关心开始/停止测量，所有细节都由Manager处理
 */
class MeasurementManager(
    private val context: Context,
    private val imageView: TpImageView,
    private val overlayView: MeasurementOverlayView,
    private val statusTextView: TextView? = null
) {
    
    companion object {
        private const val TAG = "MeasurementManager"
    }
    
    // 核心组件
    private val angleMeasureHelper = AngleMeasureHelper()
    private val fourPointAngleHelper = FourPointAngleHelper()
    private val pointMeasureHelper = PointMeasureHelper()
    private val lineMeasureHelper = LineMeasureHelper()
    private val touchHandler = MeasurementTouchHandler()

    /**
     * 测量模式枚举
     */
    enum class MeasurementMode {
        ANGLE,           // 三点角度测量
        FOUR_POINT_ANGLE, // 四点角度测量
        POINT,           // 点测量
        LINE             // 线段测量
    }

    // 状态管理
    private var isAngleMeasuring = false
    private var isFourPointAngleMeasuring = false
    private var isPointMeasuring = false
    private var isLineMeasuring = false
    private var activeMeasurementMode: MeasurementMode? = null // 当前激活的测量模式
    private var currentBitmap: Bitmap? = null
    private var isInitialized = false
    
    /**
     * 🚀 初始化测量管理器
     */
    fun initialize(bitmap: Bitmap) {
        try {
            Log.d(TAG, "🚀 Initializing MeasurementManager")
            
            currentBitmap = bitmap.copy(Bitmap.Config.ARGB_8888, true)
            
            // 设置覆盖层的ImageView引用
            overlayView.setImageView(imageView)

            // 设置覆盖层的AngleMeasureHelper引用
            overlayView.setAngleMeasureHelper(angleMeasureHelper)

            // 设置覆盖层的FourPointAngleHelper引用
            overlayView.setFourPointAngleHelper(fourPointAngleHelper)

            // 设置覆盖层的PointMeasureHelper引用
            overlayView.setPointMeasureHelper(pointMeasureHelper)

            // 设置覆盖层的LineMeasureHelper引用
            overlayView.setLineMeasureHelper(lineMeasureHelper)

            // 初始化触摸处理器
            touchHandler.initialize(imageView, angleMeasureHelper)
            
            isInitialized = true
            Log.d(TAG, "✅ MeasurementManager initialized successfully")
            
        } catch (e: Exception) {
            Log.e(TAG, "❌ Failed to initialize MeasurementManager: ${e.message}")
            e.printStackTrace()
        }
    }
    
    /**
     * 🎯 开始角度测量 - 支持混合模式
     */
    fun startAngleMeasurement(): Boolean {
        if (!isInitialized) {
            Log.e(TAG, "❌ MeasurementManager not initialized")
            return false
        }

        if (isAngleMeasuring) {
            Log.w(TAG, "⚠️ Angle measurement already in progress")
            return true
        }

        // 🔄 混合共存模式：如果四点角度测量存在，将其设为未激活状态
        if (isFourPointAngleMeasuring) {
            Log.d(TAG, "🔄 Deactivating four-point angle measurement, keeping data")
            // 不需要调用 deactivateFourPointAngleMeasurement()，直接设置新的激活模式
        }

        try {
            Log.d(TAG, "🎯 Starting angle measurement - mixed coexistence mode")

            // 1. 设置混合触摸监听器
            setupMixedTouchHandler()

            currentBitmap?.let { bitmap ->
                // 2. 🚀 初始化专业级角度测量助手
                val textView = statusTextView ?: TextView(context)
                angleMeasureHelper.init(imageView, textView, bitmap.copy(Bitmap.Config.ARGB_8888, true))

                // 3. 设置测量更新回调
                angleMeasureHelper.setMeasurementUpdateCallback {
                    updateMixedOverlayDisplay()
                }

                // 4. 🎨 设置缩放监听器
                imageView.setMatrixChangeListener {
                    if (isAngleMeasuring) {
                        angleMeasureHelper.onScaleChanged()
                    }
                    if (isFourPointAngleMeasuring) {
                        fourPointAngleHelper.onScaleChanged()
                    }
                    if (isPointMeasuring) {
                        pointMeasureHelper.onScaleChanged()
                    }
                    if (isLineMeasuring) {
                        lineMeasureHelper.onScaleChanged()
                    }
                    updateMixedOverlayDisplay()
                }

                // 5. 🚀 设置测量状态和激活模式
                isAngleMeasuring = true
                activeMeasurementMode = MeasurementMode.ANGLE
                Log.d(TAG, "🚀 [DEBUG] Set isAngleMeasuring = true, activeMeasurementMode = ANGLE")

                // 6. 🎯 直接在屏幕中心生成可拖动的角度
                val measurementId = angleMeasureHelper.startNewMeasurement()
                Log.d(TAG, "🎯 Generated draggable angle at screen center: $measurementId")

                // 7. 🔄 立即更新混合覆盖层显示 - 确保数据同步
                updateMixedOverlayDisplay()

                // 8. 🎯 强制显示机制
                forceOverlayDisplay()

                // 9. 🔄 延迟再次强制显示确保时序
                overlayView.post {
                    forceOverlayDisplay()
                }

                // 10. 更新UI提示
                statusTextView?.apply {
                    visibility = View.VISIBLE
                    text = "角度已生成！拖动任意点调整角度，长按删除"
                }

                Log.d(TAG, "✅ Angle measurement started successfully with enhanced display forcing")
                return true
            }

            Log.e(TAG, "❌ No bitmap available for measurement")
            return false

        } catch (e: Exception) {
            Log.e(TAG, "❌ Failed to start angle measurement: ${e.message}")
            e.printStackTrace()
            return false
        }
    }
    
    /**
     * ⏹️ 停止角度测量 - 极简API
     */
    fun stopAngleMeasurement() {
        if (!isAngleMeasuring) {
            Log.w(TAG, "⚠️ No angle measurement in progress")
            return
        }
        
        try {
            Log.d(TAG, "⏹️ Stopping angle measurement")
            
            // 1. 暂停测量助手（保留数据）
            angleMeasureHelper.pauseMeasurement()
            
            // 2. 恢复TpImageView的原始触摸处理
            imageView.restoreTouchListener()
            
            // 3. 隐藏覆盖层
            overlayView.visibility = View.GONE
            
            // 4. 更新状态
            isAngleMeasuring = false
            
            // 5. 隐藏状态文本
            statusTextView?.visibility = View.GONE
            
            Log.d(TAG, "✅ Angle measurement stopped - data preserved")
            
        } catch (e: Exception) {
            Log.e(TAG, "❌ Failed to stop angle measurement: ${e.message}")
            e.printStackTrace()
        }
    }

    /**
     * 🎯 开始四点角度测量 - 支持混合模式
     */
    fun startFourPointAngleMeasurement(): Boolean {
        if (!isInitialized) {
            Log.e(TAG, "❌ MeasurementManager not initialized")
            return false
        }

        if (isFourPointAngleMeasuring) {
            Log.w(TAG, "⚠️ Four-point angle measurement already in progress")
            return true
        }

        // 🔄 混合共存模式：如果三点角度测量存在，将其设为未激活状态
        if (isAngleMeasuring) {
            Log.d(TAG, "🔄 Deactivating angle measurement, keeping data")
            // 不需要调用 deactivateAngleMeasurement()，直接设置新的激活模式
        }

        try {
            Log.d(TAG, "🎯 Starting four-point angle measurement - mixed coexistence mode")

            // 1. 设置混合触摸监听器
            setupMixedTouchHandler()

            currentBitmap?.let { bitmap ->
                // 2. 🚀 初始化四点角度测量助手
                fourPointAngleHelper.init(imageView, bitmap.copy(Bitmap.Config.ARGB_8888, true))

                // 3. 设置测量更新回调
                fourPointAngleHelper.setMeasurementUpdateCallback {
                    updateMixedOverlayDisplay()
                }

                // 4. 🎨 设置缩放监听器（已在startAngleMeasurement中设置，避免重复）
                // 混合模式下统一处理缩放事件

                // 5. 🚀 设置测量状态和激活模式
                isFourPointAngleMeasuring = true
                activeMeasurementMode = MeasurementMode.FOUR_POINT_ANGLE
                Log.d(TAG, "🚀 [DEBUG] Set isFourPointAngleMeasuring = true, activeMeasurementMode = FOUR_POINT_ANGLE")

                // 6. 🎯 直接在屏幕中心生成可拖动的四点角度
                val measurementId = fourPointAngleHelper.startNewMeasurement()
                Log.d(TAG, "🎯 Generated draggable four-point angle at screen center: $measurementId")

                // 7. 🔄 立即更新混合覆盖层显示
                updateMixedOverlayDisplay()

                // 8. 🎯 强制显示机制
                forceOverlayDisplay()

                // 9. 🔄 延迟再次强制显示确保时序
                overlayView.post {
                    forceOverlayDisplay()
                }

                // 10. 更新UI提示
                statusTextView?.apply {
                    visibility = View.VISIBLE
                    text = "四点角度已生成！拖动任意点调整角度，长按删除"
                }

                Log.d(TAG, "✅ Four-point angle measurement started successfully")
                return true
            }

            Log.e(TAG, "❌ No bitmap available for four-point measurement")
            return false

        } catch (e: Exception) {
            Log.e(TAG, "❌ Failed to start four-point angle measurement: ${e.message}")
            e.printStackTrace()
            return false
        }
    }

    /**
     * ⏹️ 停止四点角度测量
     */
    fun stopFourPointAngleMeasurement() {
        if (!isFourPointAngleMeasuring) {
            Log.w(TAG, "⚠️ No four-point angle measurement in progress")
            return
        }

        try {
            Log.d(TAG, "⏹️ Stopping four-point angle measurement")

            // 1. 暂停测量助手（保留数据）
            fourPointAngleHelper.pauseMeasurement()

            // 2. 恢复TpImageView的原始触摸处理
            imageView.restoreTouchListener()

            // 3. 隐藏覆盖层
            overlayView.visibility = View.GONE

            // 4. 更新状态
            isFourPointAngleMeasuring = false

            // 5. 隐藏状态文本
            statusTextView?.visibility = View.GONE

            Log.d(TAG, "✅ Four-point angle measurement stopped - data preserved")

        } catch (e: Exception) {
            Log.e(TAG, "❌ Failed to stop four-point angle measurement: ${e.message}")
            e.printStackTrace()
        }
    }

    /**
     * 🎯 开始点测量 - 支持混合模式
     */
    fun startPointMeasurement(): Boolean {
        if (!isInitialized) {
            Log.e(TAG, "❌ MeasurementManager not initialized")
            return false
        }

        val bitmap = currentBitmap
        if (bitmap == null) {
            Log.e(TAG, "❌ No bitmap available for point measurement")
            return false
        }

        try {
            Log.d(TAG, "🎯 Starting point measurement")

            // 1. 初始化点测量助手
            pointMeasureHelper.init(imageView, bitmap)

            // 2. 设置测量更新回调
            pointMeasureHelper.setMeasurementUpdateCallback {
                updatePointMeasurementDisplay()
            }

            // 3. 在屏幕中心生成默认点
            val measurementId = pointMeasureHelper.startNewMeasurement()

            // 4. 设置混合模式触摸处理
            setupMixedTouchHandler()

            // 5. 强制显示覆盖层
            forceOverlayDisplay()

            // 6. 更新状态
            isPointMeasuring = true
            activeMeasurementMode = MeasurementMode.POINT

            // 7. 立即更新显示
            updatePointMeasurementDisplay()

            Log.d(TAG, "✅ Point measurement started successfully - ID: $measurementId")
            return true

        } catch (e: Exception) {
            Log.e(TAG, "❌ Failed to start point measurement: ${e.message}")
            e.printStackTrace()
            return false
        }
    }

    /**
     * 🛑 停止点测量
     */
    fun stopPointMeasurement() {
        if (!isPointMeasuring) {
            Log.d(TAG, "⚠️ Point measurement not active")
            return
        }

        try {
            Log.d(TAG, "⏹️ Stopping point measurement")

            // 1. 暂停测量助手（保留数据）
            pointMeasureHelper.pauseMeasurement()

            // 2. 恢复TpImageView的原始触摸处理
            imageView.restoreTouchListener()

            // 3. 隐藏覆盖层
            overlayView.visibility = View.GONE

            // 4. 更新状态
            isPointMeasuring = false

            // 5. 隐藏状态文本
            statusTextView?.visibility = View.GONE

            Log.d(TAG, "✅ Point measurement stopped - data preserved")

        } catch (e: Exception) {
            Log.e(TAG, "❌ Failed to stop point measurement: ${e.message}")
            e.printStackTrace()
        }
    }

    /**
     * 📏 开始线段测量 - 极简API
     */
    fun startLineMeasurement(): Boolean {
        if (!isInitialized) {
            Log.e(TAG, "❌ MeasurementManager not initialized")
            return false
        }

        if (isLineMeasuring) {
            Log.w(TAG, "⚠️ Line measurement already in progress")
            return true
        }

        val bitmap = currentBitmap ?: run {
            Log.e(TAG, "❌ No bitmap available for line measurement")
            return false
        }

        try {
            Log.d(TAG, "📏 Starting line measurement")

            // 1. 初始化线段测量助手
            lineMeasureHelper.init(imageView, bitmap)

            // 2. 设置测量更新回调
            lineMeasureHelper.setMeasurementUpdateCallback {
                updateLineMeasurementDisplay()
            }

            // 3. 在屏幕中心生成默认线段
            val measurementId = lineMeasureHelper.startNewMeasurement()

            // 4. 设置混合模式触摸处理
            setupMixedTouchHandler()

            // 5. 🎨 设置缩放监听器 - 与startAngleMeasurement保持一致
            imageView.setMatrixChangeListener {
                Log.d(TAG, "🔄 [DEBUG] Matrix changed - isAngleMeasuring: $isAngleMeasuring, isFourPointAngleMeasuring: $isFourPointAngleMeasuring, isPointMeasuring: $isPointMeasuring, isLineMeasuring: $isLineMeasuring")

                if (isAngleMeasuring) {
                    Log.d(TAG, "🔄 [DEBUG] Calling angleMeasureHelper.onScaleChanged()")
                    angleMeasureHelper.onScaleChanged()
                }
                if (isFourPointAngleMeasuring) {
                    Log.d(TAG, "🔄 [DEBUG] Calling fourPointAngleHelper.onScaleChanged()")
                    fourPointAngleHelper.onScaleChanged()
                }
                if (isPointMeasuring) {
                    Log.d(TAG, "🔄 [DEBUG] Calling pointMeasureHelper.onScaleChanged()")
                    pointMeasureHelper.onScaleChanged()
                }
                if (isLineMeasuring) {
                    Log.d(TAG, "🔄 [DEBUG] Calling lineMeasureHelper.onScaleChanged()")
                    lineMeasureHelper.onScaleChanged()
                }
                updateMixedOverlayDisplay()
            }

            // 6. 强制显示覆盖层
            forceOverlayDisplay()

            // 7. 更新状态
            isLineMeasuring = true
            activeMeasurementMode = MeasurementMode.LINE

            // 8. 立即更新显示
            updateLineMeasurementDisplay()

            Log.d(TAG, "✅ Line measurement started successfully - ID: $measurementId")
            return true

        } catch (e: Exception) {
            Log.e(TAG, "❌ Failed to start line measurement: ${e.message}")
            e.printStackTrace()
            return false
        }
    }

    /**
     * 🛑 停止线段测量
     */
    fun stopLineMeasurement() {
        if (!isLineMeasuring) {
            Log.d(TAG, "⚠️ Line measurement not active")
            return
        }

        try {
            Log.d(TAG, "🛑 Stopping line measurement")

            // 1. 清除选中状态
            lineMeasureHelper.clearAllSelections()

            // 2. 隐藏覆盖层
            overlayView.visibility = View.GONE

            // 3. 更新状态
            isLineMeasuring = false

            // 4. 隐藏状态文本
            statusTextView?.visibility = View.GONE

            Log.d(TAG, "✅ Line measurement stopped - data preserved")

        } catch (e: Exception) {
            Log.e(TAG, "❌ Failed to stop line measurement: ${e.message}")
            e.printStackTrace()
        }
    }

    /**
     * 🎯 强制覆盖层显示 - 复制yolo_demo的成功机制
     */
    private fun forceOverlayDisplay() {
        overlayView.visibility = View.VISIBLE
        overlayView.bringToFront()
        overlayView.invalidate()
        overlayView.requestLayout()
        Log.d(TAG, "🎯 Forced overlay display with multiple mechanisms")
    }

    /**
     * 🔄 更新覆盖层显示 - 支持多角度同时显示
     */
    private fun updateOverlayDisplay() {
        try {
            Log.d(TAG, "🔄 [DEBUG] updateOverlayDisplay called - isAngleMeasuring: $isAngleMeasuring")

            if (!isAngleMeasuring) {
                Log.d(TAG, "⚠️ [DEBUG] Not in angle measuring mode, skipping overlay update")
                return
            }

            Log.d(TAG, "📊 [DEBUG] Getting measurement data from helper...")
            val measurementData = angleMeasureHelper.getAllMeasurementData()
            Log.d(TAG, "📊 [DEBUG] Retrieved ${measurementData.size} measurements from helper")

            if (measurementData.isNotEmpty()) {
                // 🎯 显示所有角度测量
                Log.d(TAG, "🎯 [DEBUG] Setting all angle measurement data to overlay...")
                overlayView.setAllAngleMeasurementData(measurementData)

                measurementData.forEachIndexed { index, measurement ->
                    Log.d(TAG, "📊 [DEBUG] measurement[$index]: points.size=${measurement.points.size}, angle=${measurement.angle}°, isDragging=${measurement.isDragging}")
                }

                // 🎯 复制yolo_demo的多重强制显示机制
                Log.d(TAG, "🎯 [DEBUG] Forcing overlay display...")
                forceOverlayDisplay()

                // 🔄 延迟再次强制显示
                overlayView.post {
                    Log.d(TAG, "🎯 [DEBUG] Post-delayed force overlay display...")
                    forceOverlayDisplay()
                }

                Log.d(TAG, "✅ [DEBUG] Multi-angle overlay display updated successfully")
            } else {
                Log.w(TAG, "⚠️ [DEBUG] No measurement data available for overlay update - measurementData is empty!")
                Log.d(TAG, "⚠️ [DEBUG] Helper state - measurements count: ${angleMeasureHelper.getMeasurementCount()}")
                overlayView.clearMeasurement()
            }

        } catch (e: Exception) {
            Log.e(TAG, "❌ [DEBUG] Failed to update overlay: ${e.message}")
            e.printStackTrace()
        }
    }

    /**
     * 🔄 更新点测量覆盖层显示
     */
    private fun updatePointMeasurementDisplay() {
        try {
            Log.d(TAG, "🔄 [DEBUG] updatePointMeasurementDisplay called - isPointMeasuring: $isPointMeasuring")

            if (!isPointMeasuring) {
                Log.d(TAG, "⚠️ [DEBUG] Not in point measuring mode, skipping overlay update")
                return
            }

            Log.d(TAG, "📊 [DEBUG] Getting point measurement data from helper...")
            val measurementData = pointMeasureHelper.getAllMeasurementData()
            Log.d(TAG, "📊 [DEBUG] Retrieved ${measurementData.size} point measurements from helper")

            if (measurementData.isNotEmpty()) {
                Log.d(TAG, "📊 [DEBUG] Updating overlay with ${measurementData.size} point measurements")

                // 🎯 更新覆盖层的点测量数据
                overlayView.updatePointMeasurements(measurementData)

                // 🎯 复制yolo_demo的多重强制显示机制
                Log.d(TAG, "🎯 [DEBUG] Forcing overlay display...")
                forceOverlayDisplay()

                // 🔄 延迟再次强制显示
                overlayView.post {
                    Log.d(TAG, "🎯 [DEBUG] Post-delayed force overlay display...")
                    forceOverlayDisplay()
                }

                Log.d(TAG, "✅ [DEBUG] Point measurement overlay display updated successfully")
            } else {
                Log.w(TAG, "⚠️ [DEBUG] No point measurement data available for overlay update - measurementData is empty!")
                Log.d(TAG, "⚠️ [DEBUG] Helper state - measurements count: ${pointMeasureHelper.getMeasurementCount()}")
                overlayView.clearMeasurement()
            }

        } catch (e: Exception) {
            Log.e(TAG, "❌ [DEBUG] Failed to update point measurement overlay: ${e.message}")
            e.printStackTrace()
        }
    }

    /**
     * 📏 更新线段测量覆盖层显示
     */
    private fun updateLineMeasurementDisplay() {
        try {
            Log.d(TAG, "🔄 [DEBUG] updateLineMeasurementDisplay called - isLineMeasuring: $isLineMeasuring")

            if (!isLineMeasuring) {
                Log.d(TAG, "⚠️ [DEBUG] Not in line measuring mode, skipping overlay update")
                return
            }

            Log.d(TAG, "📊 [DEBUG] Getting line measurement data from helper...")
            val measurementData = lineMeasureHelper.getAllMeasurementData()
            Log.d(TAG, "📊 [DEBUG] Retrieved ${measurementData.size} line measurements from helper")

            if (measurementData.isNotEmpty()) {
                Log.d(TAG, "📊 [DEBUG] Updating overlay with ${measurementData.size} line measurements")

                // 📏 更新覆盖层的线段测量数据
                overlayView.updateLineMeasurements(measurementData)

                // 🎯 复制yolo_demo的多重强制显示机制
                Log.d(TAG, "🎯 [DEBUG] Forcing overlay display...")
                forceOverlayDisplay()

                // 🔄 延迟再次强制显示
                overlayView.post {
                    Log.d(TAG, "🎯 [DEBUG] Post-delayed force overlay display...")
                    forceOverlayDisplay()
                }

                Log.d(TAG, "✅ [DEBUG] Line measurement overlay display updated successfully")
            } else {
                Log.w(TAG, "⚠️ [DEBUG] No line measurement data available for overlay update - measurementData is empty!")
                Log.d(TAG, "⚠️ [DEBUG] Helper state - measurements count: ${lineMeasureHelper.getMeasurementCount()}")
                overlayView.clearMeasurement()
            }

        } catch (e: Exception) {
            Log.e(TAG, "❌ [DEBUG] Failed to update line measurement display: ${e.message}")
            e.printStackTrace()
        }
    }

    /**
     * 🔄 更新四点角度覆盖层显示
     */
    private fun updateFourPointOverlayDisplay() {
        try {
            Log.d(TAG, "🔄 [DEBUG] updateFourPointOverlayDisplay called - isFourPointAngleMeasuring: $isFourPointAngleMeasuring")

            if (!isFourPointAngleMeasuring) {
                Log.d(TAG, "⚠️ [DEBUG] Not in four-point angle measuring mode, skipping overlay update")
                return
            }

            Log.d(TAG, "📊 [DEBUG] Getting four-point measurement data from helper...")
            val measurementData = fourPointAngleHelper.getAllMeasurementData()
            Log.d(TAG, "📊 [DEBUG] Retrieved ${measurementData.size} four-point measurements from helper")

            if (measurementData.isNotEmpty()) {
                // 🎯 显示所有四点角度测量
                Log.d(TAG, "🎯 [DEBUG] Setting all four-point angle measurement data to overlay...")
                overlayView.setAllFourPointAngleMeasurementData(measurementData)

                measurementData.forEachIndexed { index, measurement ->
                    Log.d(TAG, "📊 [DEBUG] four-point measurement[$index]: points.size=${measurement.points.size}, angle=${measurement.angle}°, isDragging=${measurement.isDragging}")
                }

                // 🎯 强制显示机制
                Log.d(TAG, "🎯 [DEBUG] Forcing overlay display...")
                forceOverlayDisplay()

                // 🔄 延迟再次强制显示
                overlayView.post {
                    Log.d(TAG, "🎯 [DEBUG] Post-delayed force overlay display...")
                    forceOverlayDisplay()
                }

            } else {
                Log.d(TAG, "⚠️ [DEBUG] No four-point measurement data available")
                overlayView.clearMeasurement()
            }

        } catch (e: Exception) {
            Log.e(TAG, "❌ Failed to update four-point overlay display: ${e.message}")
            e.printStackTrace()
        }
    }

    /**
     * 🔄 更新混合覆盖层显示 - 基于数据存在性显示，支持数据持久化
     */
    private fun updateMixedOverlayDisplay() {
        try {
            // Log.d(TAG, "🔄 [DEBUG] updateMixedOverlayDisplay called - isAngleMeasuring: $isAngleMeasuring, isFourPointAngleMeasuring: $isFourPointAngleMeasuring")

            if (!isMixedMeasuring()) {
                // Log.d(TAG, "⚠️ [DEBUG] Not in any measurement mode, skipping overlay update")
                return
            }

            // 确保覆盖层可见
            overlayView.visibility = View.VISIBLE

            // 🎯 获取三点角度测量数据
            val angleMeasurements = angleMeasureHelper.getAllMeasurementData()
            val isAngleActive = (activeMeasurementMode == MeasurementMode.ANGLE)
            overlayView.updateAngleMeasurements(angleMeasurements)
            // Log.d(TAG, "🔄 [DEBUG] Updated ${angleMeasurements.size} three-point angle measurements (active: $isAngleActive)")

            // 🎯 获取四点角度测量数据
            val fourPointMeasurements = fourPointAngleHelper.getAllMeasurementData()
            val isFourPointActive = (activeMeasurementMode == MeasurementMode.FOUR_POINT_ANGLE)
            overlayView.updateFourPointAngleMeasurements(fourPointMeasurements)
            // Log.d(TAG, "🔄 [DEBUG] Updated ${fourPointMeasurements.size} four-point angle measurements (active: $isFourPointActive)")

            // 🎯 获取点测量数据
            val pointMeasurements = pointMeasureHelper.getAllMeasurementData()
            val isPointActive = (activeMeasurementMode == MeasurementMode.POINT)
            overlayView.updatePointMeasurements(pointMeasurements)
            // Log.d(TAG, "🔄 [DEBUG] Updated ${pointMeasurements.size} point measurements (active: $isPointActive)")

            // 强制重绘覆盖层
            overlayView.invalidate()

            // Log.d(TAG, "✅ [DEBUG] Mixed overlay display updated successfully - persistent data display enabled")

        } catch (e: Exception) {
            Log.e(TAG, "❌ Failed to update mixed overlay display: ${e.message}")
            e.printStackTrace()
        }
    }

    /**
     * 🚀 设置四点角度混合触摸处理器
     */
    private fun setupFourPointHybridTouchHandler() {
        // 恢复TpImageView的原始触摸监听器（保持缩放功能）
        imageView.restoreTouchListener()

        // 设置四点角度测量触摸处理器
        val measurementHandler = { event: android.view.MotionEvent, viewWidth: Int, viewHeight: Int ->
            var handled = false

            if (isFourPointAngleMeasuring) {
                handled = fourPointAngleHelper.handleTouchEvent(event, viewWidth, viewHeight)
            }

            handled
        }

        // 将测量处理器设置到TpImageView
        imageView.setMeasurementTouchHandler(measurementHandler)

        Log.d(TAG, "🚀 Four-point hybrid touch handler setup complete")
    }

    /**
     * 🚀 设置混合触摸处理器（支持缩放+测量）
     */
    private fun setupHybridTouchHandler() {
        // 恢复TpImageView的原始触摸监听器（保持缩放功能）
        imageView.restoreTouchListener()
        
        // 设置测量触摸处理器
        val measurementHandler = { event: android.view.MotionEvent, viewWidth: Int, viewHeight: Int ->
            var handled = false

            if (isAngleMeasuring) {
                handled = angleMeasureHelper.handleTouchEvent(event, viewWidth, viewHeight)
            }

            handled
        }
        
        // 将测量处理器设置到TpImageView
        imageView.setMeasurementTouchHandler(measurementHandler)
        
        Log.d(TAG, "🚀 Hybrid touch handler setup complete")
    }

    /**
     * 🚀 设置混合模式触摸处理器 - 智能激活对应的测量类型
     */
    private fun setupMixedTouchHandler() {
        // 恢复TpImageView的原始触摸监听器（保持缩放功能）
        imageView.restoreTouchListener()

        // 创建智能混合测量触摸处理器
        val mixedMeasurementHandler = { event: android.view.MotionEvent, viewWidth: Int, viewHeight: Int ->
            Log.d(TAG, "🎯 MeasurementManager smart touch handler called: action=${event.action}, point=(${event.x}, ${event.y})")
            var handled = false

            if (event.action == android.view.MotionEvent.ACTION_DOWN) {
                // 🎯 智能检测：检查触摸点是否在某个测量上，自动激活对应模式
                val touchPoint = android.graphics.PointF(event.x, event.y)

                // 首先检查三点角度测量
                if (isAngleMeasuring && angleMeasureHelper.isPointOnMeasurement(touchPoint)) {
                    if (activeMeasurementMode != MeasurementMode.ANGLE) {
                        Log.d(TAG, "🎯 Touch detected on three-point angle measurement - auto-activating ANGLE mode")
                        // 🔄 清除其他模式的选中状态
                        clearOtherModeSelections(MeasurementMode.ANGLE)
                        activeMeasurementMode = MeasurementMode.ANGLE
                        updateMixedOverlayDisplay()
                    }
                    Log.d(TAG, "🎯 Delegating touch to AngleMeasureHelper for three-point angle measurement")
                    handled = angleMeasureHelper.handleTouchEvent(event, viewWidth, viewHeight)
                    Log.d(TAG, "🎯 AngleMeasureHelper.handleTouchEvent returned: $handled")
                }
                // 然后检查四点角度测量
                else if (isFourPointAngleMeasuring && fourPointAngleHelper.isPointOnMeasurement(touchPoint)) {
                    if (activeMeasurementMode != MeasurementMode.FOUR_POINT_ANGLE) {
                        Log.d(TAG, "🎯 Touch detected on four-point angle measurement - auto-activating FOUR_POINT_ANGLE mode")
                        // 🔄 清除其他模式的选中状态
                        clearOtherModeSelections(MeasurementMode.FOUR_POINT_ANGLE)
                        activeMeasurementMode = MeasurementMode.FOUR_POINT_ANGLE
                        updateMixedOverlayDisplay()
                    }
                    Log.d(TAG, "🎯 Delegating touch to FourPointAngleHelper for four-point angle measurement")
                    handled = fourPointAngleHelper.handleTouchEvent(event, viewWidth, viewHeight)
                    Log.d(TAG, "🎯 FourPointAngleHelper.handleTouchEvent returned: $handled")
                }
                // 然后检查点测量
                else if (isPointMeasuring && pointMeasureHelper.isPointOnMeasurement(touchPoint)) {
                    if (activeMeasurementMode != MeasurementMode.POINT) {
                        Log.d(TAG, "🎯 Touch detected on point measurement - auto-activating POINT mode")
                        // 🔄 清除其他模式的选中状态
                        clearOtherModeSelections(MeasurementMode.POINT)
                        activeMeasurementMode = MeasurementMode.POINT
                        updateMixedOverlayDisplay()
                    }
                    Log.d(TAG, "🎯 Delegating touch to PointMeasureHelper for point measurement")
                    handled = pointMeasureHelper.handleTouchEvent(event, viewWidth, viewHeight)
                    Log.d(TAG, "🎯 PointMeasureHelper.handleTouchEvent returned: $handled")
                }
                // 最后检查线段测量
                else if (isLineMeasuring && lineMeasureHelper.isPointOnMeasurement(touchPoint)) {
                    if (activeMeasurementMode != MeasurementMode.LINE) {
                        Log.d(TAG, "🎯 Touch detected on line measurement - auto-activating LINE mode")
                        // 🔄 清除其他模式的选中状态
                        clearOtherModeSelections(MeasurementMode.LINE)
                        activeMeasurementMode = MeasurementMode.LINE
                        updateMixedOverlayDisplay()
                    }
                    Log.d(TAG, "🎯 Delegating touch to LineMeasureHelper for line measurement")
                    handled = lineMeasureHelper.handleTouchEvent(event, viewWidth, viewHeight)
                    Log.d(TAG, "🎯 LineMeasureHelper.handleTouchEvent returned: $handled")
                }
                // 如果没有触摸到任何测量，检查是否为空白区域点击
                else {
                    Log.d(TAG, "🔍 No measurement touched - checking for empty area click")
                    if (event.action == MotionEvent.ACTION_UP) {
                        Log.d(TAG, "🔍 ACTION_UP detected - checking if near any measurement")
                        // 检查是否点击了空白区域（远离所有测量）
                        val isNearAnyMeasurement =
                            (isAngleMeasuring && angleMeasureHelper.isNearAnyMeasurement(touchPoint)) ||
                            (isFourPointAngleMeasuring && fourPointAngleHelper.isNearAnyMeasurement(touchPoint)) ||
                            (isPointMeasuring && pointMeasureHelper.isNearAnyMeasurement(touchPoint)) ||
                            (isLineMeasuring && lineMeasureHelper.isNearAnyMeasurement(touchPoint))

                        Log.d(TAG, "🔍 isNearAnyMeasurement: $isNearAnyMeasurement (angle: $isAngleMeasuring, fourPoint: $isFourPointAngleMeasuring, point: $isPointMeasuring, line: $isLineMeasuring)")

                        if (!isNearAnyMeasurement) {
                            Log.d(TAG, "🔄 Clicked on empty area - clearing all selections")
                            // 清除所有选中状态
                            if (isAngleMeasuring) {
                                Log.d(TAG, "🔄 Clearing angle measurement selection")
                                angleMeasureHelper.clearSelection()
                            }
                            if (isFourPointAngleMeasuring) {
                                Log.d(TAG, "🔄 Clearing four-point angle measurement selection")
                                fourPointAngleHelper.clearSelection()
                            }
                            if (isPointMeasuring) {
                                Log.d(TAG, "🔄 Clearing point measurement selection")
                                pointMeasureHelper.clearSelection()
                            }
                            if (isLineMeasuring) {
                                Log.d(TAG, "🔄 Clearing line measurement selection")
                                lineMeasureHelper.clearSelection()
                            }
                            activeMeasurementMode = null
                            updateMixedOverlayDisplay()
                            handled = false // 不消费事件
                        } else {
                            Log.d(TAG, "🔍 Click is near measurement - keeping current selection")
                        }
                    }

                    // 如果不是空白区域点击，使用当前激活的模式处理
                    if (!handled) {
                        when (activeMeasurementMode) {
                            MeasurementMode.ANGLE -> {
                                if (isAngleMeasuring) {
                                    handled = angleMeasureHelper.handleTouchEvent(event, viewWidth, viewHeight)
                                }
                            }
                            MeasurementMode.FOUR_POINT_ANGLE -> {
                                if (isFourPointAngleMeasuring) {
                                    handled = fourPointAngleHelper.handleTouchEvent(event, viewWidth, viewHeight)
                                }
                            }
                            MeasurementMode.POINT -> {
                                if (isPointMeasuring) {
                                    handled = pointMeasureHelper.handleTouchEvent(event, viewWidth, viewHeight)
                                }
                            }
                            MeasurementMode.LINE -> {
                                if (isLineMeasuring) {
                                    handled = lineMeasureHelper.handleTouchEvent(event, viewWidth, viewHeight)
                                }
                            }
                            null -> {
                                // 静默处理
                            }
                        }
                    }
                }
            } else {
                // 对于非ACTION_DOWN事件，也需要检查空白区域点击
                val touchPoint = android.graphics.PointF(event.x, event.y)

                // 检查是否触摸到任何测量
                val touchedAngleMeasurement = isAngleMeasuring && angleMeasureHelper.isPointOnMeasurement(touchPoint)
                val touchedFourPointMeasurement = isFourPointAngleMeasuring && fourPointAngleHelper.isPointOnMeasurement(touchPoint)
                val touchedPointMeasurement = isPointMeasuring && pointMeasureHelper.isPointOnMeasurement(touchPoint)
                val touchedLineMeasurement = isLineMeasuring && lineMeasureHelper.isPointOnMeasurement(touchPoint)

                if (touchedAngleMeasurement) {
                    Log.d(TAG, "🎯 Non-ACTION_DOWN: Touched angle measurement")
                    if (isAngleMeasuring) {
                        handled = angleMeasureHelper.handleTouchEvent(event, viewWidth, viewHeight)
                    }
                } else if (touchedFourPointMeasurement) {
                    Log.d(TAG, "🎯 Non-ACTION_DOWN: Touched four-point measurement")
                    if (isFourPointAngleMeasuring) {
                        handled = fourPointAngleHelper.handleTouchEvent(event, viewWidth, viewHeight)
                    }
                } else if (touchedPointMeasurement) {
                    Log.d(TAG, "🎯 Non-ACTION_DOWN: Touched point measurement")
                    if (isPointMeasuring) {
                        handled = pointMeasureHelper.handleTouchEvent(event, viewWidth, viewHeight)
                    }
                } else if (touchedLineMeasurement) {
                    Log.d(TAG, "🎯 Non-ACTION_DOWN: Touched line measurement")
                    if (isLineMeasuring) {
                        handled = lineMeasureHelper.handleTouchEvent(event, viewWidth, viewHeight)
                    }
                } else {
                    // 没有触摸到任何测量，检查是否为空白区域点击
                    if (event.action == MotionEvent.ACTION_UP) {
                        Log.d(TAG, "🔍 Non-ACTION_DOWN: ACTION_UP detected - checking if near any measurement")
                        val isNearAnyMeasurement =
                            (isAngleMeasuring && angleMeasureHelper.isNearAnyMeasurement(touchPoint)) ||
                            (isFourPointAngleMeasuring && fourPointAngleHelper.isNearAnyMeasurement(touchPoint)) ||
                            (isPointMeasuring && pointMeasureHelper.isNearAnyMeasurement(touchPoint)) ||
                            (isLineMeasuring && lineMeasureHelper.isNearAnyMeasurement(touchPoint))

                        Log.d(TAG, "🔍 Non-ACTION_DOWN: isNearAnyMeasurement: $isNearAnyMeasurement")

                        if (!isNearAnyMeasurement) {
                            Log.d(TAG, "🔄 Non-ACTION_DOWN: Clicked on empty area - clearing all selections")
                            // 清除所有选中状态
                            if (isAngleMeasuring) {
                                Log.d(TAG, "🔄 Non-ACTION_DOWN: Clearing angle measurement selection")
                                angleMeasureHelper.clearSelection()
                            }
                            if (isFourPointAngleMeasuring) {
                                Log.d(TAG, "🔄 Non-ACTION_DOWN: Clearing four-point angle measurement selection")
                                fourPointAngleHelper.clearSelection()
                            }
                            if (isPointMeasuring) {
                                Log.d(TAG, "🔄 Non-ACTION_DOWN: Clearing point measurement selection")
                                pointMeasureHelper.clearSelection()
                            }
                            if (isLineMeasuring) {
                                Log.d(TAG, "🔄 Non-ACTION_DOWN: Clearing line measurement selection")
                                lineMeasureHelper.clearSelection()
                            }
                            activeMeasurementMode = null
                            updateMixedOverlayDisplay()
                            handled = false // 不消费事件
                        }
                    } else {
                        // 其他非ACTION_UP事件，使用当前激活的模式处理
                        when (activeMeasurementMode) {
                            MeasurementMode.ANGLE -> {
                                if (isAngleMeasuring) {
                                    handled = angleMeasureHelper.handleTouchEvent(event, viewWidth, viewHeight)
                                }
                            }
                            MeasurementMode.FOUR_POINT_ANGLE -> {
                                if (isFourPointAngleMeasuring) {
                                    handled = fourPointAngleHelper.handleTouchEvent(event, viewWidth, viewHeight)
                                }
                            }
                            MeasurementMode.POINT -> {
                                if (isPointMeasuring) {
                                    handled = pointMeasureHelper.handleTouchEvent(event, viewWidth, viewHeight)
                                }
                            }
                            MeasurementMode.LINE -> {
                                if (isLineMeasuring) {
                                    handled = lineMeasureHelper.handleTouchEvent(event, viewWidth, viewHeight)
                                }
                            }
                            null -> {
                                // 静默处理
                            }
                        }
                    }
                }
            }

            handled
        }

        // 将混合测量处理器设置到TpImageView
        imageView.setMeasurementTouchHandler(mixedMeasurementHandler)

        Log.d(TAG, "🚀 Smart mixed touch handler setup complete - active mode: $activeMeasurementMode")
    }

    /**
     * 🔍 检查是否有有效的触摸处理器
     */
    private fun hasValidTouchHandler(): Boolean {
        // 简单检查：如果任一测量模式激活，认为已有有效处理器
        return isAngleMeasuring || isFourPointAngleMeasuring || isPointMeasuring || isLineMeasuring
    }

    /**
     * 📊 状态验证方法 - 确保状态一致性（支持混合共存模式）
     */
    private fun validateState(): Boolean {
        val activeModeCount = listOf(isAngleMeasuring, isFourPointAngleMeasuring, isPointMeasuring, isLineMeasuring).count { it }

        val isValid = when {
            activeModeCount == 0 -> {
                Log.d(TAG, "✅ Valid state: No measurement mode active")
                true
            }
            activeModeCount == 1 -> {
                Log.d(TAG, "✅ Valid state: Single measurement mode active (angle=$isAngleMeasuring, fourPoint=$isFourPointAngleMeasuring, point=$isPointMeasuring, line=$isLineMeasuring)")
                true
            }
            activeModeCount > 1 -> {
                // 混合共存模式：多种测量都可以存在，但需要有明确的激活状态
                val hasActiveMode = activeMeasurementMode != null
                if (hasActiveMode) {
                    Log.d(TAG, "✅ Valid state: Mixed coexistence mode (active: $activeMeasurementMode, modes: angle=$isAngleMeasuring, fourPoint=$isFourPointAngleMeasuring, point=$isPointMeasuring, line=$isLineMeasuring)")
                    true
                } else {
                    Log.e(TAG, "❌ Invalid state: Mixed mode without active measurement type")
                    false
                }
            }
            else -> {
                Log.e(TAG, "❌ Invalid state: Unexpected condition")
                false
            }
        }
        return isValid
    }
    
    /**
     * 📊 获取当前测量状态
     */
    fun isAngleMeasuring(): Boolean = isAngleMeasuring

    /**
     * 📊 获取四点角度测量状态
     */
    fun isFourPointAngleMeasuring(): Boolean = isFourPointAngleMeasuring

    /**
     * 📊 检查是否在任何测量模式下（混合模式支持）
     */
    fun isMixedMeasuring(): Boolean = isAngleMeasuring || isFourPointAngleMeasuring || isPointMeasuring || isLineMeasuring

    /**
     * 🔄 设置激活的测量模式
     */
    private fun setActiveMode(mode: MeasurementMode) {
        activeMeasurementMode = mode
        Log.d(TAG, "🔄 Active measurement mode set to: $mode")

        // 更新触摸处理器以响应新的激活模式
        setupMixedTouchHandler()

        // 更新覆盖层显示
        updateMixedOverlayDisplay()
    }

    /**
     * 🔄 取消激活三点角度测量（保持数据，仅改变激活状态）
     */
    fun deactivateAngleMeasurement() {
        if (isAngleMeasuring) {
            Log.d(TAG, "🔄 Deactivating angle measurement, keeping data but clearing selection")
            // 清除三点角度测量的选中状态
            angleMeasureHelper.clearSelection()
            // 不改变 isAngleMeasuring 状态，不改变 activeMeasurementMode
            // 让新的激活方法来设置正确的激活模式
            Log.d(TAG, "🔄 Current activeMeasurementMode: $activeMeasurementMode (will be overridden by new activation)")
            updateMixedOverlayDisplay()
        }
    }

    /**
     * 🔄 取消激活四点角度测量（保持数据，仅改变激活状态）
     */
    fun deactivateFourPointAngleMeasurement() {
        if (isFourPointAngleMeasuring) {
            Log.d(TAG, "🔄 Deactivating four-point angle measurement, keeping data but clearing selection")
            // 清除四点角度测量的选中状态
            fourPointAngleHelper.clearSelection()
            // 不改变 isFourPointAngleMeasuring 状态，不改变 activeMeasurementMode
            // 让新的激活方法来设置正确的激活模式
            Log.d(TAG, "🔄 Current activeMeasurementMode: $activeMeasurementMode (will be overridden by new activation)")
            updateMixedOverlayDisplay()
        }
    }

    /**
     * � 取消激活点测量（保持数据，仅改变激活状态）
     */
    fun deactivatePointMeasurement() {
        if (isPointMeasuring) {
            Log.d(TAG, "🔄 Deactivating point measurement, keeping data but clearing selection")
            // 清除点测量的选中状态
            pointMeasureHelper.clearSelection()
            // 不改变 isPointMeasuring 状态，不改变 activeMeasurementMode
            // 让新的激活方法来设置正确的激活模式
            Log.d(TAG, "🔄 Current activeMeasurementMode: $activeMeasurementMode (will be overridden by new activation)")
            updateMixedOverlayDisplay()
        }
    }

    /**
     * �📊 获取当前激活的测量模式
     */
    fun getActiveMeasurementMode(): MeasurementMode? = activeMeasurementMode

    /**
     * 📊 检查指定模式是否为激活状态
     */
    fun isActiveMeasurementMode(mode: MeasurementMode): Boolean = activeMeasurementMode == mode

    /**
     * 🔄 清除其他模式的选中状态（避免多模式选中冲突）
     */
    private fun clearOtherModeSelections(activeMode: MeasurementMode) {
        when (activeMode) {
            MeasurementMode.ANGLE -> {
                // 清除四点角度测量的选中状态
                if (isFourPointAngleMeasuring) {
                    fourPointAngleHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared four-point angle measurement selections")
                }
                // 清除点测量的选中状态
                if (isPointMeasuring) {
                    pointMeasureHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared point measurement selections")
                }
                // 清除线段测量的选中状态
                if (isLineMeasuring) {
                    lineMeasureHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared line measurement selections")
                }
            }
            MeasurementMode.FOUR_POINT_ANGLE -> {
                // 清除三点角度测量的选中状态
                if (isAngleMeasuring) {
                    angleMeasureHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared three-point angle measurement selections")
                }
                // 清除点测量的选中状态
                if (isPointMeasuring) {
                    pointMeasureHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared point measurement selections")
                }
                // 清除线段测量的选中状态
                if (isLineMeasuring) {
                    lineMeasureHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared line measurement selections")
                }
            }
            MeasurementMode.POINT -> {
                // 清除三点角度测量的选中状态
                if (isAngleMeasuring) {
                    angleMeasureHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared three-point angle measurement selections")
                }
                // 清除四点角度测量的选中状态
                if (isFourPointAngleMeasuring) {
                    fourPointAngleHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared four-point angle measurement selections")
                }
                // 清除线段测量的选中状态
                if (isLineMeasuring) {
                    lineMeasureHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared line measurement selections")
                }
            }
            MeasurementMode.LINE -> {
                // 清除三点角度测量的选中状态
                if (isAngleMeasuring) {
                    angleMeasureHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared three-point angle measurement selections")
                }
                // 清除四点角度测量的选中状态
                if (isFourPointAngleMeasuring) {
                    fourPointAngleHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared four-point angle measurement selections")
                }
                // 清除点测量的选中状态
                if (isPointMeasuring) {
                    pointMeasureHelper.clearSelection()
                    Log.d(TAG, "🔄 Cleared point measurement selections")
                }
            }
        }
    }

    /**
     * 🔍 检查是否正在拖拽测量点 - 支持所有测量类型
     */
    fun isDraggingPoint(): Boolean {
        return if (isInitialized) {
            when {
                isAngleMeasuring -> angleMeasureHelper.isDraggingPoint()
                isFourPointAngleMeasuring -> fourPointAngleHelper.isDraggingPoint()
                isPointMeasuring -> pointMeasureHelper.isDraggingPoint()
                isLineMeasuring -> lineMeasureHelper.isDraggingPoint()
                else -> false
            }
        } else {
            false
        }
    }

    /**
     * 🎯 添加新的角度测量 - 在测量模式下生成新角度
     */
    fun addNewAngleMeasurement(): Boolean {
        if (!isInitialized) {
            Log.e(TAG, "❌ MeasurementManager not initialized")
            return false
        }

        if (!isAngleMeasuring) {
            Log.e(TAG, "❌ Not in angle measurement mode")
            return false
        }

        try {
            Log.d(TAG, "🎯 Adding new angle measurement...")

            // 🎯 直接添加新的角度测量
            val measurementId = angleMeasureHelper.addNewMeasurement()

            if (measurementId.isNotEmpty()) {
                // 🔄 立即更新混合覆盖层显示
                updateMixedOverlayDisplay()
                forceOverlayDisplay()

                // 延迟再次强制显示确保时序
                overlayView.post {
                    forceOverlayDisplay()
                }

                Log.d(TAG, "✅ New angle measurement added successfully: $measurementId")
                return true
            } else {
                Log.e(TAG, "❌ Failed to add new angle measurement")
                return false
            }

        } catch (e: Exception) {
            Log.e(TAG, "❌ Failed to add new angle measurement: ${e.message}")
            e.printStackTrace()
            return false
        }
    }

    /**
     * 🎯 添加新的点测量 - 在测量模式下生成新点
     */
    fun addNewPointMeasurement(): Boolean {
        if (!isInitialized) {
            Log.e(TAG, "❌ MeasurementManager not initialized")
            return false
        }

        if (!isPointMeasuring) {
            Log.e(TAG, "❌ Not in point measurement mode")
            return false
        }

        try {
            Log.d(TAG, "🎯 Adding new point measurement...")

            // 🎯 直接添加新的点测量
            val measurementId = pointMeasureHelper.addNewMeasurement()

            if (measurementId.isNotEmpty()) {
                // 🔄 立即更新混合覆盖层显示
                updateMixedOverlayDisplay()
                forceOverlayDisplay()

                // 延迟再次强制显示确保时序
                overlayView.post {
                    forceOverlayDisplay()
                }

                Log.d(TAG, "✅ New point measurement added successfully: $measurementId")
                return true
            } else {
                Log.e(TAG, "❌ Failed to add new point measurement")
                return false
            }

        } catch (e: Exception) {
            Log.e(TAG, "❌ Failed to add new point measurement: ${e.message}")
            e.printStackTrace()
            return false
        }
    }

    /**
     * 🎯 添加新的四点角度测量 - 在测量模式下生成新四点角度
     */
    fun addNewFourPointAngleMeasurement(): Boolean {
        if (!isInitialized) {
            Log.e(TAG, "❌ MeasurementManager not initialized")
            return false
        }

        if (!isFourPointAngleMeasuring) {
            Log.e(TAG, "❌ Not in four-point angle measurement mode")
            return false
        }

        try {
            Log.d(TAG, "🎯 Adding new four-point angle measurement...")

            // 🎯 直接添加新的四点角度测量
            val measurementId = fourPointAngleHelper.addNewMeasurement()

            if (measurementId.isNotEmpty()) {
                // 🔄 立即更新混合覆盖层显示
                updateMixedOverlayDisplay()
                forceOverlayDisplay()

                // 延迟再次强制显示确保时序
                overlayView.post {
                    forceOverlayDisplay()
                }

                Log.d(TAG, "✅ New four-point angle measurement added successfully: $measurementId")
                return true
            } else {
                Log.e(TAG, "❌ Failed to add new four-point angle measurement")
                return false
            }

        } catch (e: Exception) {
            Log.e(TAG, "❌ Failed to add new four-point angle measurement: ${e.message}")
            e.printStackTrace()
            return false
        }
    }

    /**
     * 📏 添加新的线段测量 - 在测量模式下生成新线段
     */
    fun addNewLineMeasurement(): Boolean {
        if (!isInitialized) {
            Log.e(TAG, "❌ MeasurementManager not initialized")
            return false
        }

        if (!isLineMeasuring) {
            Log.e(TAG, "❌ Not in line measurement mode")
            return false
        }

        try {
            Log.d(TAG, "📏 Adding new line measurement...")

            // 📏 直接添加新的线段测量
            val measurementId = lineMeasureHelper.addNewMeasurement()

            if (measurementId.isNotEmpty()) {
                // 🔄 立即更新混合覆盖层显示
                updateMixedOverlayDisplay()
                forceOverlayDisplay()

                // 延迟再次强制显示确保时序
                overlayView.post {
                    forceOverlayDisplay()
                }

                Log.d(TAG, "✅ New line measurement added successfully: $measurementId")
                return true
            } else {
                Log.e(TAG, "❌ Failed to add new line measurement")
                return false
            }

        } catch (e: Exception) {
            Log.e(TAG, "❌ Failed to add new line measurement: ${e.message}")
            e.printStackTrace()
            return false
        }
    }

    /**
     * 🗑️ 删除选中的测量 - 支持三点和四点角度测量
     */
    fun deleteSelectedMeasurement(): Boolean {
        if (!isInitialized) {
            Log.e(TAG, "❌ MeasurementManager not initialized")
            return false
        }

        // 🔍 验证状态一致性
        Log.d(TAG, "🔍 Current state before validation: isAngleMeasuring=$isAngleMeasuring, isFourPointAngleMeasuring=$isFourPointAngleMeasuring, activeMeasurementMode=$activeMeasurementMode")
        if (!validateState()) {
            Log.e(TAG, "❌ Invalid state detected, cannot delete measurement")
            return false
        }

        if (!isAngleMeasuring && !isFourPointAngleMeasuring && !isPointMeasuring && !isLineMeasuring) {
            Log.e(TAG, "❌ Not in any measurement mode")
            return false
        }

        try {
            Log.d(TAG, "🗑️ Deleting selected measurement...")

            // 🔄 智能删除：检查哪个Helper有选中的测量，直接删除
            var deleted = false

            // 先检查三点角度测量是否有选中的
            if (isAngleMeasuring && angleMeasureHelper.hasSelectedMeasurement()) {
                val measurementCount = angleMeasureHelper.getMeasurementCount()
                Log.d(TAG, "🔍 Deleting from three-point angle measurements, count: $measurementCount")

                deleted = angleMeasureHelper.deleteSelectedMeasurement()
                Log.d(TAG, "🔍 AngleMeasureHelper.deleteSelectedMeasurement() returned: $deleted")

                if (deleted) {
                    activeMeasurementMode = MeasurementMode.ANGLE // 同步激活模式
                    updateMixedOverlayDisplay()
                    forceOverlayDisplay()
                    val newCount = angleMeasureHelper.getMeasurementCount()
                    Log.d(TAG, "✅ Three-point angle measurement deleted. Count: $measurementCount -> $newCount")
                }
            }
            // 如果三点角度没有选中的，检查四点角度测量
            else if (isFourPointAngleMeasuring && fourPointAngleHelper.hasSelectedMeasurement()) {
                val measurementCount = fourPointAngleHelper.getMeasurementCount()
                Log.d(TAG, "🔍 Deleting from four-point angle measurements, count: $measurementCount")

                deleted = fourPointAngleHelper.deleteSelectedMeasurement()
                Log.d(TAG, "🔍 FourPointAngleHelper.deleteSelectedMeasurement() returned: $deleted")

                if (deleted) {
                    activeMeasurementMode = MeasurementMode.FOUR_POINT_ANGLE // 同步激活模式
                    updateMixedOverlayDisplay()
                    forceOverlayDisplay()
                    val newCount = fourPointAngleHelper.getMeasurementCount()
                    Log.d(TAG, "✅ Four-point angle measurement deleted. Count: $measurementCount -> $newCount")
                }
            }
            // 如果前面都没有选中的，检查点测量
            else if (isPointMeasuring && pointMeasureHelper.hasSelectedMeasurement()) {
                val measurementCount = pointMeasureHelper.getMeasurementCount()
                Log.d(TAG, "🔍 Deleting from point measurements, count: $measurementCount")

                deleted = pointMeasureHelper.deleteSelectedMeasurement()
                Log.d(TAG, "🔍 PointMeasureHelper.deleteSelectedMeasurement() returned: $deleted")

                if (deleted) {
                    activeMeasurementMode = MeasurementMode.POINT // 同步激活模式
                    updateMixedOverlayDisplay()
                    forceOverlayDisplay()
                    val newCount = pointMeasureHelper.getMeasurementCount()
                    Log.d(TAG, "✅ Point measurement deleted. Count: $measurementCount -> $newCount")
                }
            }
            // 如果前面都没有选中的，检查线段测量
            else if (isLineMeasuring && lineMeasureHelper.hasSelectedMeasurement()) {
                val measurementCount = lineMeasureHelper.getMeasurementCount()
                Log.d(TAG, "🔍 Deleting from line measurements, count: $measurementCount")

                deleted = lineMeasureHelper.deleteSelectedMeasurement()
                Log.d(TAG, "🔍 LineMeasureHelper.deleteSelectedMeasurement() returned: $deleted")

                if (deleted) {
                    activeMeasurementMode = MeasurementMode.LINE // 同步激活模式
                    updateMixedOverlayDisplay()
                    forceOverlayDisplay()
                    val newCount = lineMeasureHelper.getMeasurementCount()
                    Log.d(TAG, "✅ Line measurement deleted. Count: $measurementCount -> $newCount")
                }
            }

            if (!deleted) {
                Log.w(TAG, "⚠️ No selected measurement to delete")
            }

            return deleted

        } catch (e: Exception) {
            Log.e(TAG, "❌ Failed to delete selected measurement: ${e.message}")
            e.printStackTrace()
            return false
        }
    }
    
    /**
     * 🧹 清理资源
     */
    fun cleanup() {
        try {
            Log.d(TAG, "🧹 Cleaning up MeasurementManager")
            
            stopAngleMeasurement()
            angleMeasureHelper.clearAllMeasurements()
            overlayView.clearMeasurement()
            
            isInitialized = false
            currentBitmap = null
            
            Log.d(TAG, "✅ MeasurementManager cleaned up")
            
        } catch (e: Exception) {
            Log.e(TAG, "❌ Failed to cleanup MeasurementManager: ${e.message}")
        }
    }
}
