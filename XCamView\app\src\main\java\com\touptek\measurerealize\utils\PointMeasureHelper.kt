package com.touptek.measurerealize.utils

import android.graphics.*
import android.util.Log
import android.view.MotionEvent
import android.widget.ImageView
import kotlin.math.*
import java.util.*

/**
 * 🎯 专业级点测量助手 - 与XCamView架构完全一致
 * 
 * 架构特点：
 * - 🎯 与AngleMeasureHelper相同的交互逻辑
 * - 🔄 与现有测量系统相同的坐标管理
 * - 🎪 与其他Helper相同的触摸处理模式
 * - 📐 与MeasurementManager完全集成
 */

/**
 * 🎯 点测量实例 - 专业级数据结构
 */
data class PointMeasurement(
    val id: String = UUID.randomUUID().toString(),
    var viewPoints: MutableList<PointF> = mutableListOf(),  // 视图坐标（单个点）
    var bitmapPoints: MutableList<PointF> = mutableListOf(), // 位图坐标（用于保存）
    var isSelected: Boolean = false,
    var isEditing: Boolean = false,
    var isCompleted: Boolean = false,
    var textPosition: PointF? = null,
    var creationTime: Long = System.currentTimeMillis(),
    var lastModified: Long = System.currentTimeMillis()
) {

    /**
     * 🎯 检查点是否在触摸范围内
     */
    fun isPointInTouchRange(touchPoint: PointF, touchRadius: Float): Boolean {
        if (viewPoints.isEmpty()) return false
        val point = viewPoints[0]
        val distance = sqrt((touchPoint.x - point.x).pow(2) + (touchPoint.y - point.y).pow(2))
        return distance <= touchRadius
    }

    /**
     * 🎨 更新视图坐标点
     */
    fun updateViewPoint(newViewPoint: PointF) {
        if (viewPoints.isNotEmpty()) {
            viewPoints[0] = newViewPoint
            markAsModified()
        }
    }

    /**
     * 🔄 同步位图坐标
     */
    fun syncBitmapCoords(imageView: ImageView) {
        bitmapPoints.clear()
        viewPoints.forEach { viewPoint ->
            val bitmapPoint = convertViewToBitmapCoords(viewPoint, imageView)
            bitmapPoints.add(bitmapPoint)
        }
    }

    /**
     * 🔄 从位图坐标恢复视图坐标
     */
    fun syncViewCoords(imageView: ImageView) {
        if (bitmapPoints.size == viewPoints.size) {
            viewPoints.clear()
            bitmapPoints.forEach { bitmapPoint ->
                val viewPoint = convertBitmapToViewCoords(bitmapPoint, imageView)
                viewPoints.add(viewPoint)
            }
        }
    }

    /**
     * 🎯 视图坐标转位图坐标
     */
    private fun convertViewToBitmapCoords(viewPoint: PointF, imageView: ImageView): PointF {
        val matrix = imageView.imageMatrix
        val inverseMatrix = Matrix()

        return if (matrix.invert(inverseMatrix)) {
            val point = FloatArray(2) { 0f }
            point[0] = viewPoint.x
            point[1] = viewPoint.y
            inverseMatrix.mapPoints(point)
            PointF(point[0], point[1])
        } else {
            PointF(viewPoint.x, viewPoint.y)
        }
    }

    /**
     * 🎯 位图坐标转视图坐标
     */
    private fun convertBitmapToViewCoords(bitmapPoint: PointF, imageView: ImageView): PointF {
        val matrix = imageView.imageMatrix
        val point = FloatArray(2) { 0f }
        point[0] = bitmapPoint.x
        point[1] = bitmapPoint.y
        matrix.mapPoints(point)
        return PointF(point[0], point[1])
    }

    fun markAsModified() {
        lastModified = System.currentTimeMillis()
    }

    /**
     * 获取点的显示文本
     */
    fun getDisplayText(): String {
        return if (viewPoints.isNotEmpty()) {
            val point = viewPoints[0]
            String.format("点: (%.0f, %.0f)", point.x, point.y)
        } else {
            "无效点"
        }
    }
}

/**
 * 🎯 专业级点测量助手主类 - 与XCamView架构完全一致
 */
class PointMeasureHelper {
    companion object {
        private const val TAG = "PointMeasureHelper"

        // 交互参数 - 与AngleMeasureHelper保持一致
        private const val TOUCH_RADIUS = 80f           // 触摸检测半径
        private const val CLICK_TOLERANCE = 20f        // 点击容差
        private const val LONG_PRESS_DURATION = 800L   // 长按时间
    }

    // 核心组件 - 与AngleMeasureHelper保持一致的结构
    private lateinit var imageView: ImageView
    private lateinit var originalBitmap: Bitmap

    // 测量数据管理
    private val measurements = ArrayList<PointMeasurement>()
    private var activeMeasurement: PointMeasurement? = null
    private var selectedMeasurement: PointMeasurement? = null

    // 交互状态
    private var isDraggingPoint = false
    private var isCreatingNew = false

    // 触摸状态跟踪
    private var lastTouchX = 0f
    private var lastTouchY = 0f
    private var longPressStartTime = 0L

    // 回调函数
    private var measurementUpdateCallback: (() -> Unit)? = null

    /**
     * 🚀 初始化助手 - 与AngleMeasureHelper保持一致
     */
    fun init(imageView: ImageView, bitmap: Bitmap) {
        this.imageView = imageView
        this.originalBitmap = bitmap.copy(Bitmap.Config.ARGB_8888, true)

        // 清空之前的数据
        measurements.clear()
        selectedMeasurement = null
        activeMeasurement = null
        resetInteractionState()

        Log.d(TAG, "🎯 PointMeasureHelper initialized")
    }

    private fun resetInteractionState() {
        isDraggingPoint = false
        isCreatingNew = false
        lastTouchX = 0f
        lastTouchY = 0f
        longPressStartTime = 0L
    }

    /**
     * 🎯 开始新的点测量 - 在屏幕中心生成默认点
     */
    fun startNewMeasurement(): String {
        if (!::originalBitmap.isInitialized) {
            Log.w(TAG, "Bitmap not initialized")
            return ""
        }

        // 在视图中心生成点
        val viewCenterX = imageView.width / 2f
        val viewCenterY = imageView.height / 2f
        val centerPoint = PointF(viewCenterX, viewCenterY)

        // 创建测量实例
        val measurement = PointMeasurement(
            isSelected = true,
            isEditing = false,
            isCompleted = true
        )

        // 添加视图坐标点
        measurement.viewPoints.add(centerPoint)

        // 同步位图坐标
        measurement.syncBitmapCoords(imageView)

        measurement.textPosition = PointF(viewCenterX, viewCenterY + 40f)

        // 取消其他测量的选中状态
        measurements.forEach {
            it.isSelected = false
            it.isEditing = false
        }

        measurements.add(measurement)
        activeMeasurement = null
        selectedMeasurement = measurement
        isCreatingNew = false

        // 通知更新
        notifyUpdate()

        Log.d(TAG, "🎯 Generated point at screen center: ${measurement.getDisplayText()}")
        return measurement.id
    }

    /**
     * 🔔 通知更新
     */
    private fun notifyUpdate() {
        measurementUpdateCallback?.invoke()
    }

    /**
     * 🎯 设置测量更新回调
     */
    fun setMeasurementUpdateCallback(callback: () -> Unit) {
        this.measurementUpdateCallback = callback
    }

    /**
     * 📊 获取所有测量数据用于覆盖层显示
     */
    fun getAllMeasurementData(): List<PointMeasurementData> {
        return measurements.mapNotNull { measurement ->
            if (measurement.viewPoints.isNotEmpty()) {
                PointMeasurementData(
                    points = measurement.viewPoints.toList(),
                    isDragging = isDraggingPoint && measurement.isSelected,
                    isSelected = measurement.isSelected
                )
            } else null
        }
    }

    /**
     * 🎨 获取当前选中的测量数据（向后兼容）
     */
    fun getMeasurementData(): PointMeasurementData? {
        return selectedMeasurement?.let { measurement ->
            if (measurement.viewPoints.isNotEmpty()) {
                PointMeasurementData(
                    points = measurement.viewPoints.toList(),
                    isDragging = isDraggingPoint,
                    isSelected = measurement.isSelected
                )
            } else null
        }
    }

    /**
     * 🎯 处理触摸事件 - 与AngleMeasureHelper保持一致的交互逻辑
     */
    fun handleTouchEvent(event: MotionEvent, viewWidth: Int, viewHeight: Int): Boolean {
        val touchPoint = PointF(event.x, event.y)

        when (event.action) {
            MotionEvent.ACTION_DOWN -> {
                lastTouchX = touchPoint.x
                lastTouchY = touchPoint.y
                longPressStartTime = System.currentTimeMillis()

                // 检查是否点击了现有测量的点
                for (measurement in measurements.reversed()) {
                    if (measurement.isPointInTouchRange(touchPoint, TOUCH_RADIUS)) {
                        selectMeasurement(measurement)
                        startDraggingPoint(measurement)
                        Log.d(TAG, "🎯 Started dragging point")
                        return true
                    }
                }

                // 🔧 智能空白区域检测 - 避免UI按钮区域被误判
                if (isInImageContentArea(touchPoint, viewWidth, viewHeight)) {
                    measurements.forEach { it.isSelected = false }
                    selectedMeasurement = null
                    notifyUpdate()
                    Log.d(TAG, "📍 Clicked empty area in image content, deselected all measurements")
                } else {
                    Log.d(TAG, "🛡️ Clicked in UI area, preserving selection state")
                }

                return false // 让TpImageView处理缩放
            }

            MotionEvent.ACTION_MOVE -> {
                if (isDraggingPoint) {
                    selectedMeasurement?.let { measurement ->
                        // 点直接跟随手指
                        measurement.updateViewPoint(touchPoint)
                        // 同步位图坐标
                        measurement.syncBitmapCoords(imageView)
                        notifyUpdate()
                        return true
                    }
                }
                return false
            }

            MotionEvent.ACTION_UP -> {
                val wasDragging = isDraggingPoint
                val currentTime = System.currentTimeMillis()
                val touchDuration = currentTime - longPressStartTime
                val touchDistance = sqrt((touchPoint.x - lastTouchX).pow(2) + (touchPoint.y - lastTouchY).pow(2))

                isDraggingPoint = false

                if (wasDragging) {
                    notifyUpdate()
                    return true
                }

                // 长按删除
                if (touchDistance < CLICK_TOLERANCE && touchDuration > LONG_PRESS_DURATION) {
                    return handleLongPress(touchPoint)
                }

                // 🎯 轻触选中逻辑：处理短时间点击的情况
                if (touchDistance < CLICK_TOLERANCE && touchDuration < LONG_PRESS_DURATION) {
                    // 检查是否点击了测量点
                    for (measurement in measurements.reversed()) {
                        if (measurement.isPointInTouchRange(touchPoint, TOUCH_RADIUS)) {
                            // 用户轻触了测量点，设置选中状态
                            measurements.forEach { it.isSelected = false }
                            measurement.isSelected = true
                            selectedMeasurement = measurement
                            Log.d(TAG, "🎯 ACTION_UP: Light touch selection - selected measurement ${measurement.id}")
                            notifyUpdate()
                            return true
                        }
                    }
                }

                return false
            }
        }

        return false
    }

    private fun selectMeasurement(measurement: PointMeasurement) {
        measurements.forEach { it.isSelected = false }
        measurement.isSelected = true
        selectedMeasurement = measurement
        notifyUpdate()
    }

    private fun startDraggingPoint(measurement: PointMeasurement) {
        isDraggingPoint = true
        measurement.isEditing = true
    }

    private fun handleLongPress(touchPoint: PointF): Boolean {
        // 检查是否长按了某个测量的点
        for (measurement in measurements.reversed()) {
            if (measurement.isPointInTouchRange(touchPoint, TOUCH_RADIUS)) {
                // 删除测量
                measurements.remove(measurement)
                if (selectedMeasurement == measurement) {
                    selectedMeasurement = null
                }
                notifyUpdate()
                Log.d(TAG, "🗑️ Deleted measurement via long press: ${measurement.id}")
                return true
            }
        }
        return false
    }

    /**
     * 🛡️ 检测触摸点是否在图像内容区域（避免UI按钮区域）
     */
    private fun isInImageContentArea(touchPoint: PointF, viewWidth: Int, viewHeight: Int): Boolean {
        try {
            // 定义UI区域边界（顶部和底部各20%为UI区域）
            val topUIHeight = viewHeight * 0.2f
            val bottomUIStart = viewHeight * 0.8f

            // 如果触摸点在顶部或底部UI区域，不取消选中
            if (touchPoint.y < topUIHeight || touchPoint.y > bottomUIStart) {
                Log.d(TAG, "🛡️ Touch in UI area: y=${touchPoint.y}, topUI=$topUIHeight, bottomUI=$bottomUIStart")
                return false
            }

            // 中间区域认为是图像内容区域
            Log.d(TAG, "📍 Touch in image content area: y=${touchPoint.y}")
            return true

        } catch (e: Exception) {
            Log.e(TAG, "❌ Error checking image content area: ${e.message}")
            // 出错时保守处理，不取消选中
            return false
        }
    }



    /**
     * 🎯 获取测量数量
     */
    fun getMeasurementCount(): Int = measurements.size

    /**
     * 🎯 检查是否正在拖拽点
     */
    fun isDraggingPoint(): Boolean = isDraggingPoint

    /**
     * 🎯 检查是否触摸到测量点
     */
    fun isTouchingMeasurementPoint(touchPoint: PointF): Boolean {
        return measurements.any { measurement ->
            measurement.isPointInTouchRange(touchPoint, TOUCH_RADIUS)
        }
    }

    /**
     * 🎯 添加新的点测量 - 与AngleMeasureHelper保持一致
     */
    fun addNewMeasurement(): String {
        if (!::originalBitmap.isInitialized) {
            Log.w(TAG, "Bitmap not initialized")
            return ""
        }

        // 取消当前选中状态
        measurements.forEach { it.isSelected = false }

        // 创建新的点测量
        val measurementId = startNewMeasurement()

        Log.d(TAG, "🎯 Added new point measurement: $measurementId")
        return measurementId
    }

    /**
     * 🗑️ 删除选中的测量
     */
    fun deleteSelectedMeasurement(): Boolean {
        Log.d(TAG, "🔍 deleteSelectedMeasurement called - selectedMeasurement: ${selectedMeasurement?.id}")
        Log.d(TAG, "🔍 Current measurements count: ${measurements.size}")

        var selected = selectedMeasurement

        // 🔄 如果没有选中的测量，尝试自动选中最后一个测量
        if (selected == null && measurements.isNotEmpty()) {
            val lastMeasurement = measurements.last()
            lastMeasurement.isSelected = true
            selectedMeasurement = lastMeasurement
            selected = lastMeasurement
            Log.d(TAG, "🔄 Auto-selected last measurement for deletion: ${lastMeasurement.id}")
        }

        if (selected == null) {
            Log.w(TAG, "⚠️ No measurements available for deletion")
            return false
        }

        val removed = measurements.remove(selected)
        if (removed) {
            selectedMeasurement = null
            isDraggingPoint = false

            // 如果还有其他测量，选中最后一个
            if (measurements.isNotEmpty()) {
                val lastMeasurement = measurements.last()
                lastMeasurement.isSelected = true
                selectedMeasurement = lastMeasurement
            }

            notifyUpdate()
            Log.d(TAG, "🗑️ Deleted selected measurement: ${selected.id}")
            return true
        }

        return false
    }

    /**
     * ⏸️ 暂停测量
     */
    fun pauseMeasurement() {
        selectedMeasurement = null
        activeMeasurement = null
        resetInteractionState()
        Log.d(TAG, "⏸️ Measurement paused")
    }

    /**
     * ▶️ 恢复测量
     */
    fun resumeMeasurement() {
        // 恢复时不需要特殊操作，保持当前状态
    }

    /**
     * 🧹 清除所有测量
     */
    fun clearAllMeasurements() {
        measurements.clear()
        selectedMeasurement = null
        activeMeasurement = null
        resetInteractionState()
        notifyUpdate()
        Log.d(TAG, "🧹 Cleared all measurements")
    }

    /**
     * 🧹 重置助手
     */
    fun reset() {
        measurements.clear()
        selectedMeasurement = null
        activeMeasurement = null
        resetInteractionState()
        Log.d(TAG, "🧹 Helper reset")
    }

    /**
     * 🎯 检查触摸点是否在任何测量上（用于智能模式激活）
     */
    fun isPointOnMeasurement(touchPoint: PointF): Boolean {
        // 检查是否点击了现有测量的点
        for (measurement in measurements.reversed()) {
            if (measurement.isPointInTouchRange(touchPoint, TOUCH_RADIUS)) {
                return true
            }
        }
        return false
    }

    /**
     * 🔍 检查是否有选中的测量
     */
    fun hasSelectedMeasurement(): Boolean {
        return selectedMeasurement != null
    }

    /**
     * 🔍 检查触摸点是否靠近任何测量
     */
    fun isNearAnyMeasurement(touchPoint: PointF): Boolean {
        return measurements.any { measurement ->
            measurement.viewPoints.any { point ->
                val distance = sqrt((touchPoint.x - point.x).pow(2) + (touchPoint.y - point.y).pow(2))
                distance <= 100f // 100像素的容忍范围
            }
        }
    }

    /**
     * 🔄 清除所有选中状态（用于模式切换时避免冲突）
     */
    fun clearSelection() {
        Log.d(TAG, "🔄 clearSelection called - current selectedMeasurement: ${selectedMeasurement?.id}")

        selectedMeasurement = null
        measurements.forEach { it.isSelected = false }
        isDraggingPoint = false

        Log.d(TAG, "🔄 Cleared all selections in PointMeasureHelper")
        notifyUpdate()
    }

    /**
     * 🔄 处理缩放变化 - 同步所有测量的坐标
     */
    fun onScaleChanged() {
        Log.d(TAG, "🔄 Scale changed - syncing ${measurements.size} point measurements")
        // 从位图坐标恢复视图坐标，确保测量跟随图像
        measurements.forEach { measurement ->
            Log.d(TAG, "🔄 Before sync: viewPoints size=${measurement.viewPoints.size}")
            measurement.syncViewCoords(imageView)
            Log.d(TAG, "🔄 After sync: viewPoints updated for measurement ${measurement.id}")
        }
        notifyUpdate()
        Log.d(TAG, "🔄 Point coordinate sync completed")
    }
}
