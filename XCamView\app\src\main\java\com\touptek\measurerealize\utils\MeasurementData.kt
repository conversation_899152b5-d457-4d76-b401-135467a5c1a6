package com.touptek.measurerealize.utils

import android.graphics.PointF

/**
 * 🎨 测量数据基类 - 专业级测量系统
 */
sealed class MeasurementData

/**
 * 🎯 角度测量数据 - 三点角度测量
 */
data class AngleMeasurementData(
    val points: List<PointF>,           // 三个点：顶点、第一点、第二点
    val angle: Double,                  // 计算出的角度值（度）
    val isDragging: Boolean = false,    // 是否正在拖拽状态
    val isSelected: Boolean = false     // 是否为选中状态
) : MeasurementData()

/**
 * 📏 距离测量数据 - 两点距离测量
 */
data class DistanceMeasurementData(
    val points: List<PointF>,           // 两个点：起点、终点
    val distance: Double,               // 计算出的距离值
    val isDragging: Boolean = false     // 是否正在拖拽状态
) : MeasurementData()

/**
 * 📦 矩形测量数据 - 四点矩形测量
 */
data class RectangleMeasurementData(
    val points: List<PointF>,           // 四个角点
    val area: Double,                   // 面积
    val perimeter: Double,              // 周长
    val isDragging: Boolean = false     // 是否正在拖拽状态
) : MeasurementData()

/**
 * ⭕ 圆形测量数据 - 圆心+半径点测量
 */
data class CircleMeasurementData(
    val points: List<PointF>,           // 两个点：圆心、半径点
    val radius: Double,                 // 半径
    val area: Double,                   // 面积
    val perimeter: Double,              // 周长
    val isDragging: Boolean = false     // 是否正在拖拽状态
) : MeasurementData()

/**
 * 🔺 椭圆测量数据
 */
data class EllipseMeasurementData(
    val points: List<PointF>,           // 椭圆的关键点
    val area: Double,                   // 面积
    val perimeter: Double,              // 周长
    val isDragging: Boolean = false     // 是否正在拖拽状态
) : MeasurementData()

/**
 * 🔺 三点圆测量数据
 */
data class ThreePointCircleMeasurementData(
    val points: List<PointF>,           // 三个点确定圆
    val center: PointF,                 // 圆心
    val radius: Double,                 // 半径
    val area: Double,                   // 面积
    val perimeter: Double,              // 周长
    val isDragging: Boolean = false     // 是否正在拖拽状态
) : MeasurementData()

/**
 * 🔄 四点角度测量数据 - 增强版本
 */
data class FourPointAngleMeasurementData(
    val points: List<PointF>,           // 四个点构成两条线
    val intersection: PointF?,          // 两条线的交点
    val angle: Double,                  // 两条线的夹角
    val isValid: Boolean = true,        // 角度计算是否有效
    val isDragging: Boolean = false,    // 是否正在拖拽状态
    val isSelected: Boolean = false     // 是否为选中状态
) : MeasurementData()

/**
 * 📏 平行线测量数据
 */
data class ParallelLinesMeasurementData(
    val points: List<PointF>,           // 平行线的点
    val distance: Double,               // 平行线间距离
    val isDragging: Boolean = false     // 是否正在拖拽状态
) : MeasurementData()

/**
 * 📊 多点路径测量数据
 */
data class MultiPointPathMeasurementData(
    val points: List<PointF>,           // 路径上的所有点
    val totalLength: Double,            // 总长度
    val isDragging: Boolean = false     // 是否正在拖拽状态
) : MeasurementData()

/**
 * 🎯 点测量数据 - 单点标记测量
 */
data class PointMeasurementData(
    val points: List<PointF>,           // 点坐标（单个点）
    val isDragging: Boolean = false,    // 是否正在拖拽状态
    val isSelected: Boolean = false     // 是否为选中状态
) : MeasurementData()

/**
 * 📏 线段测量数据 - 双端点线段测量
 */
data class LineMeasurementData(
    val points: List<PointF>,           // 两个端点：[起点, 终点]
    val length: Double,                 // 线段长度（像素）
    val isDragging: Boolean = false,    // 是否正在拖拽状态
    val isSelected: Boolean = false     // 是否为选中状态
) : MeasurementData()
