package com.touptek.measurerealize.utils;

import java.lang.System;

/**
 * 🎨 专业级测量覆盖层 - 超越iPad体验的可视化
 *
 * 核心特性：
 * - 多层测量渲染：支持同时显示多种测量类型
 * - 专业视觉效果：动态高亮、流畅动画、精美渲染
 * - 智能坐标转换：与TpImageView完美协作
 * - 高性能绘制：优化的Canvas绘制算法
 */
@kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000\u00c6\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0006\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\b\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0011\u0018\u00002\u00020\u0001B%\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u0012\b\b\u0002\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\u0002\u0010\bJ\u0006\u0010$\u001a\u00020%JD\u0010&\u001a\u00020%2\u0006\u0010\'\u001a\u00020(2\u0006\u0010)\u001a\u00020*2\u0006\u0010+\u001a\u00020*2\u0006\u0010,\u001a\u00020*2\u0006\u0010-\u001a\u00020.2\b\b\u0002\u0010/\u001a\u0002002\b\b\u0002\u00101\u001a\u000200H\u0002J \u00102\u001a\u00020%2\u0006\u0010\'\u001a\u00020(2\u0006\u00103\u001a\u00020\u000f2\u0006\u0010\u0019\u001a\u00020\u001aH\u0002J2\u00104\u001a\u00020%2\u0006\u0010\'\u001a\u00020(2\u0006\u0010)\u001a\u00020*2\u0006\u0010-\u001a\u00020.2\u0006\u00101\u001a\u0002002\b\b\u0002\u0010/\u001a\u000200H\u0002J \u00105\u001a\u00020%2\u0006\u0010\'\u001a\u00020(2\u0006\u00103\u001a\u0002062\u0006\u0010\u0019\u001a\u00020\u001aH\u0002J \u00107\u001a\u00020%2\u0006\u0010\'\u001a\u00020(2\u0006\u00103\u001a\u0002082\u0006\u0010\u0019\u001a\u00020\u001aH\u0002J \u00109\u001a\u00020%2\u0006\u0010\'\u001a\u00020(2\u0006\u00103\u001a\u00020:2\u0006\u0010\u0019\u001a\u00020\u001aH\u0002JB\u0010;\u001a\u00020%2\u0006\u0010\'\u001a\u00020(2\f\u0010<\u001a\b\u0012\u0004\u0012\u00020*0\n2\u0006\u0010=\u001a\u00020*2\u0006\u0010-\u001a\u00020.2\b\b\u0002\u0010/\u001a\u0002002\b\b\u0002\u00101\u001a\u000200H\u0002J \u0010>\u001a\u00020%2\u0006\u0010\'\u001a\u00020(2\u0006\u00103\u001a\u00020\u000b2\u0006\u0010\u0019\u001a\u00020\u001aH\u0002J2\u0010?\u001a\u00020%2\u0006\u0010\'\u001a\u00020(2\u0006\u0010=\u001a\u00020*2\u0006\u0010-\u001a\u00020.2\u0006\u00101\u001a\u0002002\b\b\u0002\u0010/\u001a\u000200H\u0002J8\u0010@\u001a\u00020%2\u0006\u0010\'\u001a\u00020(2\f\u0010<\u001a\b\u0012\u0004\u0012\u00020*0\n2\b\u0010=\u001a\u0004\u0018\u00010*2\u0006\u0010/\u001a\u0002002\u0006\u00101\u001a\u000200H\u0002J \u0010A\u001a\u00020%2\u0006\u0010\'\u001a\u00020(2\u0006\u00103\u001a\u00020\r2\u0006\u0010\u0019\u001a\u00020\u001aH\u0002J \u0010B\u001a\u00020%2\u0006\u0010\'\u001a\u00020(2\u0006\u00103\u001a\u00020C2\u0006\u0010\u0019\u001a\u00020\u001aH\u0002J \u0010D\u001a\u00020%2\u0006\u0010\'\u001a\u00020(2\u0006\u00103\u001a\u00020E2\u0006\u0010\u0019\u001a\u00020\u001aH\u0002J \u0010F\u001a\u00020%2\u0006\u0010\'\u001a\u00020(2\u0006\u00103\u001a\u00020\u00112\u0006\u0010\u0019\u001a\u00020\u001aH\u0002J \u0010G\u001a\u00020%2\u0006\u0010\'\u001a\u00020(2\u0006\u00103\u001a\u00020H2\u0006\u0010\u0019\u001a\u00020\u001aH\u0002J \u0010I\u001a\u00020%2\u0006\u0010\'\u001a\u00020(2\u0006\u00103\u001a\u00020J2\u0006\u0010\u0019\u001a\u00020\u001aH\u0002J\u0010\u0010K\u001a\u00020%2\u0006\u0010L\u001a\u00020MH\u0002J\u0010\u0010N\u001a\u00020%2\u0006\u0010\'\u001a\u00020(H\u0014J\u0010\u0010O\u001a\u0002002\u0006\u0010L\u001a\u00020MH\u0016J\u0014\u0010P\u001a\u00020%2\f\u0010Q\u001a\b\u0012\u0004\u0012\u00020\u000f0\nJ\u0014\u0010R\u001a\u00020%2\f\u0010Q\u001a\b\u0012\u0004\u0012\u00020\u000b0\nJ\u000e\u0010S\u001a\u00020%2\u0006\u0010T\u001a\u00020\u0013J\u000e\u0010U\u001a\u00020%2\u0006\u0010T\u001a\u00020\u0017J\u000e\u0010V\u001a\u00020%2\u0006\u0010\u0019\u001a\u00020\u001aJ\u000e\u0010W\u001a\u00020%2\u0006\u0010T\u001a\u00020\u001cJ\u0010\u0010X\u001a\u00020%2\b\u00103\u001a\u0004\u0018\u00010\u001fJ\u000e\u0010Y\u001a\u00020%2\u0006\u0010T\u001a\u00020!J\u0014\u0010Z\u001a\u00020%2\f\u0010Q\u001a\b\u0012\u0004\u0012\u00020\u000f0\nJ\u0014\u0010[\u001a\u00020%2\f\u0010Q\u001a\b\u0012\u0004\u0012\u00020\u000b0\nJ\u0014\u0010\\\u001a\u00020%2\f\u0010Q\u001a\b\u0012\u0004\u0012\u00020\r0\nJ\u0014\u0010]\u001a\u00020%2\f\u0010Q\u001a\b\u0012\u0004\u0012\u00020\u00110\nR\u0014\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u000b0\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0014\u0010\f\u001a\b\u0012\u0004\u0012\u00020\r0\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u000f0\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u00110\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0012\u001a\u0004\u0018\u00010\u0013X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0014\u001a\u00020\u0015X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0016\u001a\u0004\u0018\u00010\u0017X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0018\u001a\u00020\u0015X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0019\u001a\u0004\u0018\u00010\u001aX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u001b\u001a\u0004\u0018\u00010\u001cX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001d\u001a\u00020\u0015X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u001e\u001a\u0004\u0018\u00010\u001fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010 \u001a\u0004\u0018\u00010!X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\"\u001a\u00020\u0015X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010#\u001a\u00020\u0015X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006^"}, d2 = {"Lcom/touptek/measurerealize/utils/MeasurementOverlayView;", "Landroid/view/View;", "context", "Landroid/content/Context;", "attrs", "Landroid/util/AttributeSet;", "defStyleAttr", "", "(Landroid/content/Context;Landroid/util/AttributeSet;I)V", "allFourPointAngleMeasurementData", "", "Lcom/touptek/measurerealize/utils/FourPointAngleMeasurementData;", "allLineMeasurementData", "Lcom/touptek/measurerealize/utils/LineMeasurementData;", "allMeasurementData", "Lcom/touptek/measurerealize/utils/AngleMeasurementData;", "allPointMeasurementData", "Lcom/touptek/measurerealize/utils/PointMeasurementData;", "angleMeasureHelper", "Lcom/touptek/measurerealize/utils/AngleMeasureHelper;", "arcPaint", "Landroid/graphics/Paint;", "fourPointAngleHelper", "Lcom/touptek/measurerealize/utils/FourPointAngleHelper;", "highlightPointPaint", "imageView", "Lcom/touptek/measurerealize/TpImageView;", "lineMeasureHelper", "Lcom/touptek/measurerealize/utils/LineMeasureHelper;", "linePaint", "measurementData", "Lcom/touptek/measurerealize/utils/MeasurementData;", "pointMeasureHelper", "Lcom/touptek/measurerealize/utils/PointMeasureHelper;", "pointPaint", "textPaint", "clearMeasurement", "", "drawAngleArc", "canvas", "Landroid/graphics/Canvas;", "vertex", "Landroid/graphics/PointF;", "point1", "point2", "angle", "", "isSelected", "", "isDragging", "drawAngleMeasurement", "data", "drawAngleText", "drawCircleMeasurement", "Lcom/touptek/measurerealize/utils/CircleMeasurementData;", "drawDistanceMeasurement", "Lcom/touptek/measurerealize/utils/DistanceMeasurementData;", "drawEllipseMeasurement", "Lcom/touptek/measurerealize/utils/EllipseMeasurementData;", "drawFourPointAngleArc", "points", "intersection", "drawFourPointAngleMeasurement", "drawFourPointAngleText", "drawFourPointExtensionLines", "drawLineMeasurement", "drawMultiPointPathMeasurement", "Lcom/touptek/measurerealize/utils/MultiPointPathMeasurementData;", "drawParallelLinesMeasurement", "Lcom/touptek/measurerealize/utils/ParallelLinesMeasurementData;", "drawPointMeasurement", "drawRectangleMeasurement", "Lcom/touptek/measurerealize/utils/RectangleMeasurementData;", "drawThreePointCircleMeasurement", "Lcom/touptek/measurerealize/utils/ThreePointCircleMeasurementData;", "forwardEventToImageView", "event", "Landroid/view/MotionEvent;", "onDraw", "onTouchEvent", "setAllAngleMeasurementData", "dataList", "setAllFourPointAngleMeasurementData", "setAngleMeasureHelper", "helper", "setFourPointAngleHelper", "setImageView", "setLineMeasureHelper", "setMeasurementData", "setPointMeasureHelper", "updateAngleMeasurements", "updateFourPointAngleMeasurements", "updateLineMeasurements", "updatePointMeasurements", "app_debug"})
public final class MeasurementOverlayView extends android.view.View {
    private final android.graphics.Paint linePaint = null;
    private final android.graphics.Paint pointPaint = null;
    private final android.graphics.Paint highlightPointPaint = null;
    private final android.graphics.Paint textPaint = null;
    private final android.graphics.Paint arcPaint = null;
    private com.touptek.measurerealize.utils.MeasurementData measurementData;
    private java.util.List<com.touptek.measurerealize.utils.AngleMeasurementData> allMeasurementData;
    private java.util.List<com.touptek.measurerealize.utils.FourPointAngleMeasurementData> allFourPointAngleMeasurementData;
    private java.util.List<com.touptek.measurerealize.utils.PointMeasurementData> allPointMeasurementData;
    private java.util.List<com.touptek.measurerealize.utils.LineMeasurementData> allLineMeasurementData;
    private com.touptek.measurerealize.TpImageView imageView;
    private com.touptek.measurerealize.utils.AngleMeasureHelper angleMeasureHelper;
    private com.touptek.measurerealize.utils.FourPointAngleHelper fourPointAngleHelper;
    private com.touptek.measurerealize.utils.PointMeasureHelper pointMeasureHelper;
    private com.touptek.measurerealize.utils.LineMeasureHelper lineMeasureHelper;
    
    @kotlin.jvm.JvmOverloads
    public MeasurementOverlayView(@org.jetbrains.annotations.NotNull
    android.content.Context context) {
        super(null);
    }
    
    @kotlin.jvm.JvmOverloads
    public MeasurementOverlayView(@org.jetbrains.annotations.NotNull
    android.content.Context context, @org.jetbrains.annotations.Nullable
    android.util.AttributeSet attrs) {
        super(null);
    }
    
    @kotlin.jvm.JvmOverloads
    public MeasurementOverlayView(@org.jetbrains.annotations.NotNull
    android.content.Context context, @org.jetbrains.annotations.Nullable
    android.util.AttributeSet attrs, int defStyleAttr) {
        super(null);
    }
    
    public final void setImageView(@org.jetbrains.annotations.NotNull
    com.touptek.measurerealize.TpImageView imageView) {
    }
    
    public final void setAngleMeasureHelper(@org.jetbrains.annotations.NotNull
    com.touptek.measurerealize.utils.AngleMeasureHelper helper) {
    }
    
    public final void setFourPointAngleHelper(@org.jetbrains.annotations.NotNull
    com.touptek.measurerealize.utils.FourPointAngleHelper helper) {
    }
    
    public final void setPointMeasureHelper(@org.jetbrains.annotations.NotNull
    com.touptek.measurerealize.utils.PointMeasureHelper helper) {
    }
    
    public final void setLineMeasureHelper(@org.jetbrains.annotations.NotNull
    com.touptek.measurerealize.utils.LineMeasureHelper helper) {
    }
    
    public final void setMeasurementData(@org.jetbrains.annotations.Nullable
    com.touptek.measurerealize.utils.MeasurementData data) {
    }
    
    public final void clearMeasurement() {
    }
    
    /**
     * 🎯 设置多个角度测量数据 - 支持同时显示多个角度
     */
    public final void setAllAngleMeasurementData(@org.jetbrains.annotations.NotNull
    java.util.List<com.touptek.measurerealize.utils.AngleMeasurementData> dataList) {
    }
    
    /**
     * 🎯 设置多个四点角度测量数据 - 支持同时显示多个四点角度
     */
    public final void setAllFourPointAngleMeasurementData(@org.jetbrains.annotations.NotNull
    java.util.List<com.touptek.measurerealize.utils.FourPointAngleMeasurementData> dataList) {
    }
    
    /**
     * 🎯 更新三点角度测量数据 - 混合模式支持
     */
    public final void updateAngleMeasurements(@org.jetbrains.annotations.NotNull
    java.util.List<com.touptek.measurerealize.utils.AngleMeasurementData> dataList) {
    }
    
    /**
     * 🎯 更新四点角度测量数据 - 混合模式支持
     */
    public final void updateFourPointAngleMeasurements(@org.jetbrains.annotations.NotNull
    java.util.List<com.touptek.measurerealize.utils.FourPointAngleMeasurementData> dataList) {
    }
    
    /**
     * 🎯 更新点测量数据 - 混合模式支持
     */
    public final void updatePointMeasurements(@org.jetbrains.annotations.NotNull
    java.util.List<com.touptek.measurerealize.utils.PointMeasurementData> dataList) {
    }
    
    /**
     * 📏 更新线段测量数据 - 混合模式支持
     */
    public final void updateLineMeasurements(@org.jetbrains.annotations.NotNull
    java.util.List<com.touptek.measurerealize.utils.LineMeasurementData> dataList) {
    }
    
    @java.lang.Override
    protected void onDraw(@org.jetbrains.annotations.NotNull
    android.graphics.Canvas canvas) {
    }
    
    /**
     * 🎯 绘制角度测量 - 专业级三点角度可视化
     */
    private final void drawAngleMeasurement(android.graphics.Canvas canvas, com.touptek.measurerealize.utils.AngleMeasurementData data, com.touptek.measurerealize.TpImageView imageView) {
    }
    
    /**
     * 🎨 绘制角度弧线 - 专业级弧线渲染
     */
    private final void drawAngleArc(android.graphics.Canvas canvas, android.graphics.PointF vertex, android.graphics.PointF point1, android.graphics.PointF point2, double angle, boolean isSelected, boolean isDragging) {
    }
    
    /**
     * 🎨 绘制角度文本 - 智能位置和专业样式
     */
    private final void drawAngleText(android.graphics.Canvas canvas, android.graphics.PointF vertex, double angle, boolean isDragging, boolean isSelected) {
    }
    
    private final void drawDistanceMeasurement(android.graphics.Canvas canvas, com.touptek.measurerealize.utils.DistanceMeasurementData data, com.touptek.measurerealize.TpImageView imageView) {
    }
    
    private final void drawRectangleMeasurement(android.graphics.Canvas canvas, com.touptek.measurerealize.utils.RectangleMeasurementData data, com.touptek.measurerealize.TpImageView imageView) {
    }
    
    private final void drawCircleMeasurement(android.graphics.Canvas canvas, com.touptek.measurerealize.utils.CircleMeasurementData data, com.touptek.measurerealize.TpImageView imageView) {
    }
    
    private final void drawThreePointCircleMeasurement(android.graphics.Canvas canvas, com.touptek.measurerealize.utils.ThreePointCircleMeasurementData data, com.touptek.measurerealize.TpImageView imageView) {
    }
    
    private final void drawFourPointAngleMeasurement(android.graphics.Canvas canvas, com.touptek.measurerealize.utils.FourPointAngleMeasurementData data, com.touptek.measurerealize.TpImageView imageView) {
    }
    
    /**
     * 🎨 绘制四点角度弧线
     */
    private final void drawFourPointAngleArc(android.graphics.Canvas canvas, java.util.List<? extends android.graphics.PointF> points, android.graphics.PointF intersection, double angle, boolean isSelected, boolean isDragging) {
    }
    
    /**
     * 🎨 绘制四点角度文本
     */
    private final void drawFourPointAngleText(android.graphics.Canvas canvas, android.graphics.PointF intersection, double angle, boolean isDragging, boolean isSelected) {
    }
    
    private final void drawParallelLinesMeasurement(android.graphics.Canvas canvas, com.touptek.measurerealize.utils.ParallelLinesMeasurementData data, com.touptek.measurerealize.TpImageView imageView) {
    }
    
    private final void drawEllipseMeasurement(android.graphics.Canvas canvas, com.touptek.measurerealize.utils.EllipseMeasurementData data, com.touptek.measurerealize.TpImageView imageView) {
    }
    
    private final void drawMultiPointPathMeasurement(android.graphics.Canvas canvas, com.touptek.measurerealize.utils.MultiPointPathMeasurementData data, com.touptek.measurerealize.TpImageView imageView) {
    }
    
    /**
     * 🎯 处理触摸事件 - 委托给测量助手
     */
    @java.lang.Override
    public boolean onTouchEvent(@org.jetbrains.annotations.NotNull
    android.view.MotionEvent event) {
        return false;
    }
    
    /**
     * 🔄 将事件转发给TpImageView
     */
    private final void forwardEventToImageView(android.view.MotionEvent event) {
    }
    
    /**
     * 🎨 绘制四点角度智能延长线
     */
    private final void drawFourPointExtensionLines(android.graphics.Canvas canvas, java.util.List<? extends android.graphics.PointF> points, android.graphics.PointF intersection, boolean isSelected, boolean isDragging) {
    }
    
    /**
     * 🎯 绘制点测量 - 红色圆点+白色十字
     */
    private final void drawPointMeasurement(android.graphics.Canvas canvas, com.touptek.measurerealize.utils.PointMeasurementData data, com.touptek.measurerealize.TpImageView imageView) {
    }
    
    /**
     * 📏 绘制线段测量 - 与AngleMeasureHelper和PointMeasureHelper保持一致的状态视觉反馈
     */
    private final void drawLineMeasurement(android.graphics.Canvas canvas, com.touptek.measurerealize.utils.LineMeasurementData data, com.touptek.measurerealize.TpImageView imageView) {
    }
}