package com.touptek.measurerealize.utils;

import java.lang.System;

/**
 * 📏 线段测量实例 - 专业级数据结构
 */
@kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000R\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010!\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0004\n\u0002\u0010\t\n\u0002\b\u001a\n\u0002\u0010\u0006\n\u0002\b\f\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0010\b\n\u0002\b\u0004\n\u0002\u0010\u0007\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0004\b\u0086\b\u0018\u00002\u00020\u0001Bm\u0012\b\b\u0002\u0010\u0002\u001a\u00020\u0003\u0012\u000e\b\u0002\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005\u0012\u000e\b\u0002\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005\u0012\b\b\u0002\u0010\b\u001a\u00020\t\u0012\b\b\u0002\u0010\n\u001a\u00020\t\u0012\b\b\u0002\u0010\u000b\u001a\u00020\t\u0012\n\b\u0002\u0010\f\u001a\u0004\u0018\u00010\u0006\u0012\b\b\u0002\u0010\r\u001a\u00020\u000e\u0012\b\b\u0002\u0010\u000f\u001a\u00020\u000e\u00a2\u0006\u0002\u0010\u0010J\u0006\u0010(\u001a\u00020)J\t\u0010*\u001a\u00020\u0003H\u00c6\u0003J\u000f\u0010+\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005H\u00c6\u0003J\u000f\u0010,\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005H\u00c6\u0003J\t\u0010-\u001a\u00020\tH\u00c6\u0003J\t\u0010.\u001a\u00020\tH\u00c6\u0003J\t\u0010/\u001a\u00020\tH\u00c6\u0003J\u000b\u00100\u001a\u0004\u0018\u00010\u0006H\u00c6\u0003J\t\u00101\u001a\u00020\u000eH\u00c6\u0003J\t\u00102\u001a\u00020\u000eH\u00c6\u0003J\u0018\u00103\u001a\u00020\u00062\u0006\u00104\u001a\u00020\u00062\u0006\u00105\u001a\u000206H\u0002J\u0018\u00107\u001a\u00020\u00062\u0006\u00108\u001a\u00020\u00062\u0006\u00105\u001a\u000206H\u0002Jq\u00109\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\u000e\b\u0002\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u00052\u000e\b\u0002\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\u00060\u00052\b\b\u0002\u0010\b\u001a\u00020\t2\b\b\u0002\u0010\n\u001a\u00020\t2\b\b\u0002\u0010\u000b\u001a\u00020\t2\n\b\u0002\u0010\f\u001a\u0004\u0018\u00010\u00062\b\b\u0002\u0010\r\u001a\u00020\u000e2\b\b\u0002\u0010\u000f\u001a\u00020\u000eH\u00c6\u0001J\u0013\u0010:\u001a\u00020\t2\b\u0010;\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\u000e\u0010<\u001a\u00020=2\u0006\u0010>\u001a\u00020\u0006J\t\u0010?\u001a\u00020=H\u00d6\u0001J\u0016\u0010@\u001a\u00020\t2\u0006\u0010>\u001a\u00020\u00062\u0006\u0010A\u001a\u00020BJ\u000e\u0010C\u001a\u00020D2\u0006\u00105\u001a\u000206J\u000e\u0010E\u001a\u00020D2\u0006\u00105\u001a\u000206J\t\u0010F\u001a\u00020\u0003H\u00d6\u0001J\u0006\u0010G\u001a\u00020DR \u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0011\u0010\u0012\"\u0004\b\u0013\u0010\u0014R\u001a\u0010\r\u001a\u00020\u000eX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0015\u0010\u0016\"\u0004\b\u0017\u0010\u0018R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0019\u0010\u001aR\u001a\u0010\u000b\u001a\u00020\tX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u000b\u0010\u001b\"\u0004\b\u001c\u0010\u001dR\u001a\u0010\n\u001a\u00020\tX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\n\u0010\u001b\"\u0004\b\u001e\u0010\u001dR\u001a\u0010\b\u001a\u00020\tX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\b\u0010\u001b\"\u0004\b\u001f\u0010\u001dR\u001a\u0010\u000f\u001a\u00020\u000eX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b \u0010\u0016\"\u0004\b!\u0010\u0018R\u001c\u0010\f\u001a\u0004\u0018\u00010\u0006X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\"\u0010#\"\u0004\b$\u0010%R \u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b&\u0010\u0012\"\u0004\b\'\u0010\u0014\u00a8\u0006H"}, d2 = {"Lcom/touptek/measurerealize/utils/LineMeasurement;", "", "id", "", "viewPoints", "", "Landroid/graphics/PointF;", "bitmapPoints", "isSelected", "", "isEditing", "isCompleted", "textPosition", "creationTime", "", "lastModified", "(Ljava/lang/String;Ljava/util/List;Ljava/util/List;ZZZLandroid/graphics/PointF;JJ)V", "getBitmapPoints", "()Ljava/util/List;", "setBitmapPoints", "(Ljava/util/List;)V", "getCreationTime", "()J", "setCreationTime", "(J)V", "getId", "()Ljava/lang/String;", "()Z", "setCompleted", "(Z)V", "setEditing", "setSelected", "getLastModified", "setLastModified", "getTextPosition", "()Landroid/graphics/PointF;", "setTextPosition", "(Landroid/graphics/PointF;)V", "getViewPoints", "setViewPoints", "calculateLength", "", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "convertBitmapToViewCoords", "bitmapPoint", "imageView", "Landroid/widget/ImageView;", "convertViewToBitmapCoords", "viewPoint", "copy", "equals", "other", "getNearestPointIndex", "", "touchPoint", "hashCode", "isPointInTouchRange", "radius", "", "syncBitmapCoords", "", "syncViewCoords", "toString", "updateTextPosition", "app_debug"})
public final class LineMeasurement {
    @org.jetbrains.annotations.NotNull
    private final java.lang.String id = null;
    @org.jetbrains.annotations.NotNull
    private java.util.List<android.graphics.PointF> viewPoints;
    @org.jetbrains.annotations.NotNull
    private java.util.List<android.graphics.PointF> bitmapPoints;
    private boolean isSelected;
    private boolean isEditing;
    private boolean isCompleted;
    @org.jetbrains.annotations.Nullable
    private android.graphics.PointF textPosition;
    private long creationTime;
    private long lastModified;
    
    /**
     * 📏 线段测量实例 - 专业级数据结构
     */
    @org.jetbrains.annotations.NotNull
    public final com.touptek.measurerealize.utils.LineMeasurement copy(@org.jetbrains.annotations.NotNull
    java.lang.String id, @org.jetbrains.annotations.NotNull
    java.util.List<android.graphics.PointF> viewPoints, @org.jetbrains.annotations.NotNull
    java.util.List<android.graphics.PointF> bitmapPoints, boolean isSelected, boolean isEditing, boolean isCompleted, @org.jetbrains.annotations.Nullable
    android.graphics.PointF textPosition, long creationTime, long lastModified) {
        return null;
    }
    
    /**
     * 📏 线段测量实例 - 专业级数据结构
     */
    @java.lang.Override
    public boolean equals(@org.jetbrains.annotations.Nullable
    java.lang.Object other) {
        return false;
    }
    
    /**
     * 📏 线段测量实例 - 专业级数据结构
     */
    @java.lang.Override
    public int hashCode() {
        return 0;
    }
    
    /**
     * 📏 线段测量实例 - 专业级数据结构
     */
    @org.jetbrains.annotations.NotNull
    @java.lang.Override
    public java.lang.String toString() {
        return null;
    }
    
    public LineMeasurement() {
        super();
    }
    
    public LineMeasurement(@org.jetbrains.annotations.NotNull
    java.lang.String id, @org.jetbrains.annotations.NotNull
    java.util.List<android.graphics.PointF> viewPoints, @org.jetbrains.annotations.NotNull
    java.util.List<android.graphics.PointF> bitmapPoints, boolean isSelected, boolean isEditing, boolean isCompleted, @org.jetbrains.annotations.Nullable
    android.graphics.PointF textPosition, long creationTime, long lastModified) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getId() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<android.graphics.PointF> component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<android.graphics.PointF> getViewPoints() {
        return null;
    }
    
    public final void setViewPoints(@org.jetbrains.annotations.NotNull
    java.util.List<android.graphics.PointF> p0) {
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<android.graphics.PointF> component3() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<android.graphics.PointF> getBitmapPoints() {
        return null;
    }
    
    public final void setBitmapPoints(@org.jetbrains.annotations.NotNull
    java.util.List<android.graphics.PointF> p0) {
    }
    
    public final boolean component4() {
        return false;
    }
    
    public final boolean isSelected() {
        return false;
    }
    
    public final void setSelected(boolean p0) {
    }
    
    public final boolean component5() {
        return false;
    }
    
    public final boolean isEditing() {
        return false;
    }
    
    public final void setEditing(boolean p0) {
    }
    
    public final boolean component6() {
        return false;
    }
    
    public final boolean isCompleted() {
        return false;
    }
    
    public final void setCompleted(boolean p0) {
    }
    
    @org.jetbrains.annotations.Nullable
    public final android.graphics.PointF component7() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final android.graphics.PointF getTextPosition() {
        return null;
    }
    
    public final void setTextPosition(@org.jetbrains.annotations.Nullable
    android.graphics.PointF p0) {
    }
    
    public final long component8() {
        return 0L;
    }
    
    public final long getCreationTime() {
        return 0L;
    }
    
    public final void setCreationTime(long p0) {
    }
    
    public final long component9() {
        return 0L;
    }
    
    public final long getLastModified() {
        return 0L;
    }
    
    public final void setLastModified(long p0) {
    }
    
    /**
     * 📏 计算线段长度（使用位图坐标 - 真实长度，不受缩放影响）
     */
    public final double calculateLength() {
        return 0.0;
    }
    
    /**
     * 🔄 同步位图坐标
     */
    public final void syncBitmapCoords(@org.jetbrains.annotations.NotNull
    android.widget.ImageView imageView) {
    }
    
    /**
     * 🔄 同步视图坐标（缩放变化时调用） - 与AngleMeasureHelper保持一致
     */
    public final void syncViewCoords(@org.jetbrains.annotations.NotNull
    android.widget.ImageView imageView) {
    }
    
    /**
     * 🎯 视图坐标转位图坐标
     */
    private final android.graphics.PointF convertViewToBitmapCoords(android.graphics.PointF viewPoint, android.widget.ImageView imageView) {
        return null;
    }
    
    /**
     * 🎯 位图坐标转视图坐标
     */
    private final android.graphics.PointF convertBitmapToViewCoords(android.graphics.PointF bitmapPoint, android.widget.ImageView imageView) {
        return null;
    }
    
    /**
     * 🎯 检查触摸点是否在端点范围内
     */
    public final boolean isPointInTouchRange(@org.jetbrains.annotations.NotNull
    android.graphics.PointF touchPoint, float radius) {
        return false;
    }
    
    /**
     * 🎯 获取最近的端点索引
     */
    public final int getNearestPointIndex(@org.jetbrains.annotations.NotNull
    android.graphics.PointF touchPoint) {
        return 0;
    }
    
    /**
     * 🎯 更新文本位置（线段中点上方）
     */
    public final void updateTextPosition() {
    }
}